# main.py
import contextlib
from fastapi import FastAP<PERSON>
from mcp_echo import mcp as mcp_echo
from mcp_math import mcp as mcp_math 
import uvicorn

@contextlib.asynccontextmanager
async def lifespan(app: FastAPI):
    async with contextlib.AsyncExitStack() as stack:
        await stack.enter_async_context(mcp_echo.session_manager.run())
        await stack.enter_async_context(mcp_math.session_manager.run())
        yield


app = FastAPI(lifespan=lifespan)
app.mount("/echo/", mcp_echo.streamable_http_app())
app.mount("/math/", mcp_math.streamable_http_app())


if __name__ == "__main__":
    uvicorn.run("main:app",
                host="0.0.0.0",
                port=8766,
                workers=4, 
                reload=True,
                proxy_headers=True,
                forwarded_allow_ips="*",
                log_level="info") 
    
