#!/usr/bin/env python
# -*- coding:utf-8 -*-

import subprocess

# Example command
command = ['.venv/Scripts/python', 'tools/spider_with_playwright.py', 'https://mp.weixin.qq.com/s/Xdlfw4Pc0XCRnJs3DEOdUQ']

# Run the command and capture the output
result = subprocess.run(command, capture_output=True, text=True, encoding='utf-8')

print(f'Output: {result.stdout}')
print(f'Error: {result.stderr}')
print(f'Return code: {result.returncode}')
