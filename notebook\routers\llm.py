from fastapi import APIRouter, Body, Request, Depends
from fastapi.responses import StreamingResponse
import json

from langchain_core.messages import AIMessageChunk, AIMessage, ToolMessage
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.store.postgres.aio import AsyncPostgresStore
from ..config import get_settings
from ..schemas.llm import  LLMRequest
import logging
import traceback
import re

from contextlib import asynccontextmanager
from fastapi import FastAPI
from ..libs.graphiti_lib import get_graphiti_graph, init_graphiti_prompt
from ..libs.graphiti_lib import AsyncWorker, GraphitiSettings, get_graphiti
from ..libs.llm_lib import *

chat_logger = logging.getLogger("chat")
root_logger = logging.getLogger("root")

async_worker = AsyncWorker()

@asynccontextmanager
async def lifespan_worker(_: FastAPI):
    await async_worker.start()
    yield
    await async_worker.stop()

router = APIRouter(prefix='/llm', tags=['调用LLM Endpoint'], lifespan=lifespan_worker)
settings = get_settings()
        
async def error_generator(request:Request):
    c = {"error":"小助手开小差了，换个问题试试吧。"}
    yield  f'data: {c}\n\n'

async def event_graphiti(request:Request, message, context, chat_history, model, tenant_id, user_id, thread_config):
    """Generate SSE events from the app stream."""

    async with request.app.state.postgres_pool.connection() as conn:
        checkpoint = AsyncPostgresSaver(conn)
        store = AsyncPostgresStore(
            conn,
            index={
            "dims":settings.embedding_dim,
            "embed":init_embeddings_model(),
            "index":"data"
        })
        
        try:
            await checkpoint.setup()
        except Exception as e:
            pass
        
        try:
            await store.setup()
        except Exception as e:
            pass

        graphiti_settings = GraphitiSettings(**{
            "openai_api_key": settings.qwen_api_key,
            "openai_base_url": settings.qwen_base_url,
            "model_name": settings.qwen_model_name,
            "embedding_model": settings.qwen_embedding_model,
            "embedding_dim": settings.qwen_embedding_dim,
            "neo4j_uri": settings.neo4j_uri,
            "neo4j_user": settings.neo4j_user,
            "neo4j_password": settings.neo4j_password
        })
        
        async with get_graphiti(graphiti_settings) as graphiti:
            graph = await get_graphiti_graph(model, thread_config, store, graphiti, async_worker, tenant_id, user_id)
            prompt = init_graphiti_prompt(message, context, chat_history)
        
            app = graph.compile(checkpointer=checkpoint, store=store)     
            try:
                pre_event = ""
                cur_event = ""
                rec_seq = 1
                
                async for chunk in app.astream(prompt, config=thread_config, stream_mode="messages"):
                    if await request.is_disconnected():
                        break
                    
                    #(AIMessageChunk(content='binary', additional_kwargs={}, response_metadata={}, id='run-53fa0672-66e3-449a-b3bf-5e70011595e4'), {'thread_id': '10_10', 'user_id': '10', 'langgraph_step': 66, 'langgraph_node': 'grade_documents', 'langgraph_triggers': ['branch:call_tools:condition:grade_documents'], 'langgraph_path': ('__pregel_pull', 'grade_documents'), 'langgraph_checkpoint_ns': 'grade_documents:d25e2db0-c991-3e39-cb5b-5ac3673aab4c', 'checkpoint_ns': 'grade_documents:d25e2db0-c991-3e39-cb5b-5ac3673aab4c', 'ls_provider': 'openai', 'ls_model_name': 'gpt-4o-mini', 'ls_model_type': 'chat', 'ls_temperature': 1.0, 'ls_max_tokens': 16384})
                    #(ToolMessage(content='图片\n为人父母，要知道人云亦云的工作，并不一定能让子女过上想要的生活。\n你以为的稳定，在儿女看来，可能是在浪费生命；\n你以为的体面，在他们看来，也可能是温水煮青蛙。\n巴菲特曾说：\n“你必须能够控制自己，不要让外界左右你的理智。”\n学会保持清醒，不盲目跟风，儿女才能在自己事业的蓝天上，飞得更高，更远。\n图片\n很认同郑渊洁的一句话：\n“铁饭碗的真实含义不是在一个地方吃一辈子饭，而是一辈子到哪儿都有饭吃。”\n聪明的父母，正是因为洞见了这一点，所以总能站在更广阔的视角，指导儿女的职业生涯。\n那么，他们都会建议儿女从事什么样的工作呢？\n让我们一起来看～\n1.能力与理想相匹配的工作\n常听人说：“工作的终极目标，就是要获得成就感。”\n成就感如何得来的呢？\n《圆桌派》里，许子东老师曾分享过一个“成就感公式”：\n成就感=能力/理想。\n也就是说，当一个人，选对了理想的工作，并且他的能力，足够胜任这份工作时，工作的成就感，就能达到最大。\n图片\n根据“成就感公式”，父母可以让子女，对自身做一个综合的评估。\n别因为随大流，忽视内心真实的渴求；\n也别因为好高骛远，忽视能力的发展。\n如此，才能找到能力与理想之间的平衡点，在工作时，干劲十足。\n2.可持续增值的工作\n管理学上，有一个“蘑菇效应”：\n蘑菇刚开始生长的时候，并不引人注目，只有长到足够的高度，才会脱颖而出。\n很多工作也是这样，虽然刚开始，要经历一段蛰伏期，但越往后，越增值。\n那些曾经沉淀下来的时间，都将化为经验和阅历，支撑一个人在职业生涯中，稳稳站住脚跟。\n所以，在选择工作时，还要衡量它的增值空间。\n那些“越老越吃香”的工作，都是就业时不错的选择。\n3.需要「智力支撑」的工作\n李开复曾说：\n“未来15年内，会有越来越多的人工智能，接管我们现在的工作和任务。”\n对于很多大厂而言，体力劳动力并不稀缺，稀缺的是脑力劳动力。\n图片\n毕竟，再高效的机器，也替代不了创造性的工作；\n再精准的算法，也无法洞见各种幽微的情绪。\n有远见的父母，会建议儿女从事“要动脑”的工作。\n因为他们深知，智慧，永远是人类保持不可替代性的核心能力。\n图片\n作家刘润曾感慨，进入21世纪以来，发展速度是以十年、甚至几年为单位计算的。\n日新月异的时代，涌现出了很多新兴职业，也淘汰了很多传统行业。\n有远见的父母，不再拘泥于所谓的铁饭碗，而是引导儿女在追梦之路上，活出自己的精彩。\n很喜欢海蓝博士的一句话：\n\n日新月异的时代，涌现出了很多新兴职业，也淘汰了很多传统行业。\n有远见的父母，不再拘泥于所谓的铁饭碗，而是引导儿女在追梦之路上，活出自己的精彩。\n很喜欢海蓝博士的一句话：\n当一个人，做好自己喜欢和擅长的事时，就会成为优秀人才，甚至稀缺人才，收入也一定会比其他人高。\n最关键的是：每天都会感到充实和快乐。\n其实，为人父母，我们对儿女的要求，不就是工作顺心、生活无忧么？\n与其让儿女“随大流”，不如让他们听从内心的意愿，做适合自己的选择；\n与其让儿女“赚大钱”，不如让他们先夯实能力的基础，占据行业的高地。\n有格局的父母都会认识到，无论哪一份工作，成长都比光鲜可靠，发展也比稳定重要。\n点个\n图片\n，愿每个父母，都能做好孩子的引路人，让他们去做有价值的事，成为想成为的人；\n也愿天下所有的儿女，都能在工作中找到追寻的诗和远方，从此人生向阳，岁月有光。\n作者 | 竹西，爱读书，爱生活。\n主播 | 夏萌，用我的声音温暖你的睡前时光。\n图片 | 视觉中国，网络（如有侵权请联系删除）\n▼点击下方卡片 发现更多美文\n图片\n预览时标签不可点\n名称已清空\n微信扫一扫赞赏作者\n喜欢作者其它金额\n文章\n暂无文章\n喜欢作者\n其它金额\n最低赞赏 ¥0\n其它金额\n赞赏金额\n最低赞赏 ¥0\n搜索「」网络结果\n留言\n暂无留言\n已无更多数据\n发消息\n写留言:\n写留言\n表情\n微信扫一扫 关注该公众号\n继续滑动看下一个\n十点读书\n向上滑动看下一个\n当前内容可能存在未经审核的第三方商业营销信息，请确认是否继续访问。\n继续访问取消\n微信公众平台广告规范指引\n知道了\n微信扫一扫 使用小程序\n取消 允许\n取消 允许\n作者头像\n微信扫一扫可打开此内容， 使用完整服务\n十点读书\n留言\n暂无留言\n已无更多数据\n发消息\n写留言:\n写留言\n表情\n0个朋友\n前往「发现 > 看一看」看更多朋友\n： ， ， ， ， ， ， ， ， ， ， ， ， 。 视频 小程序 赞 ，轻点两下取消赞 在看 ，轻点两下取消在看 分享 留言 收藏 听过\n十点读书\n越穷的人，越喜欢让子女从事这2种工作，结果越来越穷\n选择留言身份\n该账号因违规无法跳转\n投诉已提交\n请选择补充原因\n\n市面上，很多工作看似轻松，却最容易养废一个人。\n想起一位作者说的：\n“如果工作几年后，你除了工资之外一无所获，只剩下远去的青春，和消磨的时间，这不亚于人生的一场劫难。”\n当工作者工作了十几年，都没有培养出核心竞争力，那么ta被淘汰的风险，也会大大提高。\n毕竟，只出售简单劳动力的工作，是很容易被替代的。\n有想法的父母，都懂得提醒儿女，在找工作时，不要因为短期的利益，而忽视长远的发展。\n图片\n你的身边，是否也有这样的父母？\n他们看到什么工作热门，就让孩子报什么，全然不顾孩子的意愿和能力。\n这样的孩子，看似有一份光鲜体面的工作，可每天却过得像行尸走肉。\n曾在书上看到这样一个故事：\n李明的大学专业是文秘，毕业后去政府单位，一直是他的梦想。\n可校招时，他才发现，想进政府，必须经过残酷的公务员考试，名额实在有限。\n李明班上的很多同学，因为担心考公的风险，都另辟蹊径，签了工程单位。\n看到大家都找到了工作，李明有些紧张。\n在一次聊天中，他告诉了父母自己的迷茫。\n父母劝他，现在公务员难考，不如和大家一起，先进工程单位，毕竟多数人的选择，总没有错。\n李明虽然不喜欢工程项目部“吃沙子”的日子，但在父母的再三说服下，还是和某路桥公司签约了。\n工作以后，他才发现，工地上的环境，自己极其不适应。\n不仅大学学的知识，在这里完全没有发挥的空间，而且因为工期紧，他每天都要加班。\n李明曾多次打电话告诉父母，说自己受不了工地的工作，想回家考公务员。\n可父母却责备他不能吃苦，说：\n“大家都是这么过来的，忍一忍不就好了？！”\n于是，李明只能继续待在不喜欢的岗位上，每天过着自己深恶痛绝的日子。\n图片\n听过一个有趣的比喻：\n从事不适合的工作，就像和一个不爱的人结婚，每天都过得缺乏激情，郁郁寡欢。\n或许有一些父母会劝孩子：\n“第一份工作，再怎么苦也要熬下去。”\n“现在跳槽出去，没有经验，谁会要你？”\n殊不知，如果一份工作，对以后的事业方向毫无帮助，那就是浪费时间。\n毕淑敏曾在即将获得博士学位的情况下，转而投身写作。\n当亲友们为她惋惜时，她却说：\n生命对我来说是那么宝贵，不值得专门拿出半年的时间，去学外语。\n如今，毕淑敏已经成为了一名著名的作家。\n少有人走的路，看似辛苦，却也成就了她的梦想。\n图片\n为人父母，要知道人云亦云的工作，并不一定能让子女过上想要的生活。\n你以为的稳定，在儿女看来，可能是在浪费生命；\n你以为的体面，在他们看来，也可能是温水煮青蛙。', name='retrieve', id='29b90e76-eadb-425f-91d6-d9417ce546bc', tool_call_id='call_up5Yat25ujlrTz3YPVtx6s3M'), {'thread_id': '10_10', 'user_id': '10', 'langgraph_step': 65, 'langgraph_node': 'call_tools', 'langgraph_triggers': ['branch:agent:tools_condition:call_tools'], 'langgraph_path': ('__pregel_pull', 'call_tools'), 'langgraph_checkpoint_ns': 'call_tools:90e841d7-7cc5-0f0c-a0c5-51b75dfeaee9'})
                    #root_logger.info(chunk)
                    
                    c = chunk[0].content            
                    data = ""
                    if type(chunk[0]) == ToolMessage:
                        if chunk[0].name == "tavily_search" and c != "":
                            datas = json.loads(c)
                            events = []
                            for rs in datas["results"]:
                                j_c = json.dumps({"v":rs['url']}, ensure_ascii=False)
                                events.append(f"event:search\ndata: {j_c}")
                            data = "\n\n".join(events) + "\n\n"
                            pre_event = cur_event
                            cur_event = "search"
                            
                        # RAG的结果不向用户展示
                        if chunk[0].name == "retrieve":
                            data = ""
                            
                        # 调用工具生成图片
                        if chunk[0].name == "create_image":
                            img_data = chunk[0].artifact[0].data
                            img_mime = chunk[0].artifact[0].mimeType
                            j_c = json.dumps({"v":f"data:{img_mime};base64,{img_data}"}, ensure_ascii=False)
                            data = f'event:image\ndata: {j_c}\n\n'
                            pre_event = cur_event
                            cur_event = "image"
                            
                    elif type(chunk[0]) == AIMessageChunk or type(chunk[0]) == AIMessage:
                        if chunk[1]['langgraph_node'] in ['grade_documents', "rewrite", "router_content"]: 
                            data = ""
                            
                        if chunk[1]['langgraph_node'] in ['assistant', 'agent'] and c != "":
                            j_c = json.dumps({"v":c}, ensure_ascii=False)
                            data = f'event:think\ndata: {j_c}\n\n'
                            pre_event = cur_event
                            cur_event = "think"
                        
                        if chunk[1]['langgraph_node'] in ['generate'] and c != "":
                            j_c = json.dumps({"v":c}, ensure_ascii=False)
                            data = f'event:message\ndata: {j_c}\n\n'
                            pre_event = cur_event
                            cur_event = "message"
                            
                        if chunk[1]['langgraph_node'] == "recommend_question" and c != "":
                            cs = c.split("\n")
                            if len(cs) == 2:
                                if re.search(r"\bI\b", cs[0]):
                                    pre_event = cur_event 
                                    yield f"event:{pre_event}-end\ndata: {{}}\n\n"
                                    tmp = cs[0].replace("I", "").strip().strip(".")
                                    j_c = json.dumps({"v":tmp}, ensure_ascii=False)
                                    data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                                    yield data
                                    yield f"event:recommend{rec_seq}-end\ndata: {{}}\n\n"
                                    rec_seq = rec_seq + 1
                                    cur_event = f"recommend{rec_seq}"
                                    pre_event = cur_event
                                    data = ""
                                else:
                                    j_c = json.dumps({"v":cs[0]}, ensure_ascii=False)
                                    data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                                    yield data
                                    yield f"event:{pre_event}-end\ndata: {{}}\n\n"
                                    data = ""
                                    rec_seq = rec_seq + 1
                                    cur_event = f"recommend{rec_seq}"
                                    pre_event = cur_event

                                if re.search(r"\bII\b", cs[1]):
                                    tmp = cs[1].replace("II", "").strip().strip(".")
                                    j_c = json.dumps({"v":tmp}, ensure_ascii=False)
                                    data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                                if  re.search(r"\bIII\b", cs[1]):
                                    tmp = cs[1].replace("III", "").strip().strip(".")
                                    j_c = json.dumps({"v":tmp}, ensure_ascii=False)
                                    data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                            else:
                                if re.search(r"\bI\b", c):
                                    rec_seq = 1
                                    c = c.replace("I", "").strip().strip(".")
                                if  re.search(r"\bII\b", c):
                                    rec_seq = 2
                                    c = c.replace("II", "").strip().strip(".")
                                if re.search(r"\bIII\b", c):
                                    rec_seq = 3
                                    c = c.replace("III", "").strip().strip(".")
                                c = c.strip().strip(".")
                                j_c = json.dumps({"v":c}, ensure_ascii=False)
                                data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                                pre_event = cur_event
                                cur_event = f"recommend{rec_seq}"
                            
                    if pre_event == "":
                        pre_event = cur_event
                                    
                    if data != "":
                        if cur_event != pre_event:
                            yield f"event:{pre_event}-end\ndata: {{}}\n\n"
                        yield data
                            
                c = json.dumps({"done":True})
                yield f"event:{pre_event}-end\ndata: {{}}\n\n"
                yield f'data: {c}\n\n'
                
            except Exception as e:
                root_logger.error(f"Streaming error: {traceback.format_exc()}")
                c = {"error":"小助手开小差了，换个问题试试吧。"}
                yield  f'data: {c}\n\n'

async def event_generator(request:Request, message, context, chat_history, image, model, datasource, embeddings, tenant_id, user_id, search, rag, thread_config):
    """Generate SSE events from the app stream."""

    async with request.app.state.postgres_pool.connection() as conn:
        mcp_client = request.app.state.mcp_client
        checkpoint = AsyncPostgresSaver(conn)
        store = AsyncPostgresStore(
            conn,
            index={
            "dims":settings.embedding_dim,
            "embed":init_embeddings_model(),
            "index":"data"
        })
        
        try:
            await checkpoint.setup()
        except Exception as e:
            pass
        
        try:
            await store.setup()
        except Exception as e:
            pass
                            
        if image and len(image) > 0:
            if message == None or message == "":
                if len(image) >= 2:
                    message = "这几幅图片哪个更好"
                else:
                    message = "详细描述图片中的内容"
            if embeddings and len(embeddings) > 0:    
                message = retrieve_prompt_from_embeddings(tenant_id, embeddings, user_id) + ", 以上是文档内容.\n" + message
            
            graph = await get_chat_graph_with_image(model, thread_config, store)

        elif (message == None or message == "") and embeddings and len(embeddings) > 0:
            
            message = retrieve_prompt_from_embeddings(tenant_id, embeddings, user_id) + ", 总结上述文档内容."
            graph = await get_chat_graph(model, thread_config, mcp_client, store)
        elif message.startswith("画一张"):
            
            graph = await get_chat_graph_with_draw(model, thread_config, store)
        elif search:
            
            graph = await get_chat_graph_with_search(model, thread_config, store)
        elif rag:
            
            graph = await get_chat_graph_with_rag(model, thread_config, tenant_id, datasource, embeddings, store)
        else:
            
            graph = await get_chat_graph(model, thread_config, mcp_client, store)
        
        prompt = init_prompt_template(message, image, context, chat_history)
    
        app = graph.compile(checkpointer=checkpoint, store=store)     
        try:
            pre_event = ""
            cur_event = ""
            rec_seq = 1
            
            async for chunk in app.astream(prompt, config=thread_config, stream_mode="messages"):
                if await request.is_disconnected():  
                    break
                
                #(AIMessageChunk(content='binary', additional_kwargs={}, response_metadata={}, id='run-53fa0672-66e3-449a-b3bf-5e70011595e4'), {'thread_id': '10_10', 'user_id': '10', 'langgraph_step': 66, 'langgraph_node': 'grade_documents', 'langgraph_triggers': ['branch:call_tools:condition:grade_documents'], 'langgraph_path': ('__pregel_pull', 'grade_documents'), 'langgraph_checkpoint_ns': 'grade_documents:d25e2db0-c991-3e39-cb5b-5ac3673aab4c', 'checkpoint_ns': 'grade_documents:d25e2db0-c991-3e39-cb5b-5ac3673aab4c', 'ls_provider': 'openai', 'ls_model_name': 'gpt-4o-mini', 'ls_model_type': 'chat', 'ls_temperature': 1.0, 'ls_max_tokens': 16384})
                #(ToolMessage(content='图片\n为人父母，要知道人云亦云的工作，并不一定能让子女过上想要的生活。\n你以为的稳定，在儿女看来，可能是在浪费生命；\n你以为的体面，在他们看来，也可能是温水煮青蛙。\n巴菲特曾说：\n“你必须能够控制自己，不要让外界左右你的理智。”\n学会保持清醒，不盲目跟风，儿女才能在自己事业的蓝天上，飞得更高，更远。\n图片\n很认同郑渊洁的一句话：\n“铁饭碗的真实含义不是在一个地方吃一辈子饭，而是一辈子到哪儿都有饭吃。”\n聪明的父母，正是因为洞见了这一点，所以总能站在更广阔的视角，指导儿女的职业生涯。\n那么，他们都会建议儿女从事什么样的工作呢？\n让我们一起来看～\n1.能力与理想相匹配的工作\n常听人说：“工作的终极目标，就是要获得成就感。”\n成就感如何得来的呢？\n《圆桌派》里，许子东老师曾分享过一个“成就感公式”：\n成就感=能力/理想。\n也就是说，当一个人，选对了理想的工作，并且他的能力，足够胜任这份工作时，工作的成就感，就能达到最大。\n图片\n根据“成就感公式”，父母可以让子女，对自身做一个综合的评估。\n别因为随大流，忽视内心真实的渴求；\n也别因为好高骛远，忽视能力的发展。\n如此，才能找到能力与理想之间的平衡点，在工作时，干劲十足。\n2.可持续增值的工作\n管理学上，有一个“蘑菇效应”：\n蘑菇刚开始生长的时候，并不引人注目，只有长到足够的高度，才会脱颖而出。\n很多工作也是这样，虽然刚开始，要经历一段蛰伏期，但越往后，越增值。\n那些曾经沉淀下来的时间，都将化为经验和阅历，支撑一个人在职业生涯中，稳稳站住脚跟。\n所以，在选择工作时，还要衡量它的增值空间。\n那些“越老越吃香”的工作，都是就业时不错的选择。\n3.需要「智力支撑」的工作\n李开复曾说：\n“未来15年内，会有越来越多的人工智能，接管我们现在的工作和任务。”\n对于很多大厂而言，体力劳动力并不稀缺，稀缺的是脑力劳动力。\n图片\n毕竟，再高效的机器，也替代不了创造性的工作；\n再精准的算法，也无法洞见各种幽微的情绪。\n有远见的父母，会建议儿女从事“要动脑”的工作。\n因为他们深知，智慧，永远是人类保持不可替代性的核心能力。\n图片\n作家刘润曾感慨，进入21世纪以来，发展速度是以十年、甚至几年为单位计算的。\n日新月异的时代，涌现出了很多新兴职业，也淘汰了很多传统行业。\n有远见的父母，不再拘泥于所谓的铁饭碗，而是引导儿女在追梦之路上，活出自己的精彩。\n很喜欢海蓝博士的一句话：\n\n日新月异的时代，涌现出了很多新兴职业，也淘汰了很多传统行业。\n有远见的父母，不再拘泥于所谓的铁饭碗，而是引导儿女在追梦之路上，活出自己的精彩。\n很喜欢海蓝博士的一句话：\n当一个人，做好自己喜欢和擅长的事时，就会成为优秀人才，甚至稀缺人才，收入也一定会比其他人高。\n最关键的是：每天都会感到充实和快乐。\n其实，为人父母，我们对儿女的要求，不就是工作顺心、生活无忧么？\n与其让儿女“随大流”，不如让他们听从内心的意愿，做适合自己的选择；\n与其让儿女“赚大钱”，不如让他们先夯实能力的基础，占据行业的高地。\n有格局的父母都会认识到，无论哪一份工作，成长都比光鲜可靠，发展也比稳定重要。\n点个\n图片\n，愿每个父母，都能做好孩子的引路人，让他们去做有价值的事，成为想成为的人；\n也愿天下所有的儿女，都能在工作中找到追寻的诗和远方，从此人生向阳，岁月有光。\n作者 | 竹西，爱读书，爱生活。\n主播 | 夏萌，用我的声音温暖你的睡前时光。\n图片 | 视觉中国，网络（如有侵权请联系删除）\n▼点击下方卡片 发现更多美文\n图片\n预览时标签不可点\n名称已清空\n微信扫一扫赞赏作者\n喜欢作者其它金额\n文章\n暂无文章\n喜欢作者\n其它金额\n最低赞赏 ¥0\n其它金额\n赞赏金额\n最低赞赏 ¥0\n搜索「」网络结果\n留言\n暂无留言\n已无更多数据\n发消息\n写留言:\n写留言\n表情\n微信扫一扫 关注该公众号\n继续滑动看下一个\n十点读书\n向上滑动看下一个\n当前内容可能存在未经审核的第三方商业营销信息，请确认是否继续访问。\n继续访问取消\n微信公众平台广告规范指引\n知道了\n微信扫一扫 使用小程序\n取消 允许\n取消 允许\n作者头像\n微信扫一扫可打开此内容， 使用完整服务\n十点读书\n留言\n暂无留言\n已无更多数据\n发消息\n写留言:\n写留言\n表情\n0个朋友\n前往「发现 > 看一看」看更多朋友\n： ， ， ， ， ， ， ， ， ， ， ， ， 。 视频 小程序 赞 ，轻点两下取消赞 在看 ，轻点两下取消在看 分享 留言 收藏 听过\n十点读书\n越穷的人，越喜欢让子女从事这2种工作，结果越来越穷\n选择留言身份\n该账号因违规无法跳转\n投诉已提交\n请选择补充原因\n\n市面上，很多工作看似轻松，却最容易养废一个人。\n想起一位作者说的：\n“如果工作几年后，你除了工资之外一无所获，只剩下远去的青春，和消磨的时间，这不亚于人生的一场劫难。”\n当工作者工作了十几年，都没有培养出核心竞争力，那么ta被淘汰的风险，也会大大提高。\n毕竟，只出售简单劳动力的工作，是很容易被替代的。\n有想法的父母，都懂得提醒儿女，在找工作时，不要因为短期的利益，而忽视长远的发展。\n图片\n你的身边，是否也有这样的父母？\n他们看到什么工作热门，就让孩子报什么，全然不顾孩子的意愿和能力。\n这样的孩子，看似有一份光鲜体面的工作，可每天却过得像行尸走肉。\n曾在书上看到这样一个故事：\n李明的大学专业是文秘，毕业后去政府单位，一直是他的梦想。\n可校招时，他才发现，想进政府，必须经过残酷的公务员考试，名额实在有限。\n李明班上的很多同学，因为担心考公的风险，都另辟蹊径，签了工程单位。\n看到大家都找到了工作，李明有些紧张。\n在一次聊天中，他告诉了父母自己的迷茫。\n父母劝他，现在公务员难考，不如和大家一起，先进工程单位，毕竟多数人的选择，总没有错。\n李明虽然不喜欢工程项目部“吃沙子”的日子，但在父母的再三说服下，还是和某路桥公司签约了。\n工作以后，他才发现，工地上的环境，自己极其不适应。\n不仅大学学的知识，在这里完全没有发挥的空间，而且因为工期紧，他每天都要加班。\n李明曾多次打电话告诉父母，说自己受不了工地的工作，想回家考公务员。\n可父母却责备他不能吃苦，说：\n“大家都是这么过来的，忍一忍不就好了？！”\n于是，李明只能继续待在不喜欢的岗位上，每天过着自己深恶痛绝的日子。\n图片\n听过一个有趣的比喻：\n从事不适合的工作，就像和一个不爱的人结婚，每天都过得缺乏激情，郁郁寡欢。\n或许有一些父母会劝孩子：\n“第一份工作，再怎么苦也要熬下去。”\n“现在跳槽出去，没有经验，谁会要你？”\n殊不知，如果一份工作，对以后的事业方向毫无帮助，那就是浪费时间。\n毕淑敏曾在即将获得博士学位的情况下，转而投身写作。\n当亲友们为她惋惜时，她却说：\n生命对我来说是那么宝贵，不值得专门拿出半年的时间，去学外语。\n如今，毕淑敏已经成为了一名著名的作家。\n少有人走的路，看似辛苦，却也成就了她的梦想。\n图片\n为人父母，要知道人云亦云的工作，并不一定能让子女过上想要的生活。\n你以为的稳定，在儿女看来，可能是在浪费生命；\n你以为的体面，在他们看来，也可能是温水煮青蛙。', name='retrieve', id='29b90e76-eadb-425f-91d6-d9417ce546bc', tool_call_id='call_up5Yat25ujlrTz3YPVtx6s3M'), {'thread_id': '10_10', 'user_id': '10', 'langgraph_step': 65, 'langgraph_node': 'call_tools', 'langgraph_triggers': ['branch:agent:tools_condition:call_tools'], 'langgraph_path': ('__pregel_pull', 'call_tools'), 'langgraph_checkpoint_ns': 'call_tools:90e841d7-7cc5-0f0c-a0c5-51b75dfeaee9'})
                #root_logger.info(chunk)
                
                c = chunk[0].content            
                data = ""
                if type(chunk[0]) == ToolMessage:
                    if chunk[0].name == "tavily_search" and c != "":
                        datas = json.loads(c)
                        events = []
                        for rs in datas["results"]:
                            j_c = json.dumps({"v":rs['url']}, ensure_ascii=False)
                            events.append(f"event:search\ndata: {j_c}")
                        data = "\n\n".join(events) + "\n\n"
                        pre_event = cur_event
                        cur_event = "search"
                        
                    # RAG的结果不向用户展示
                    if chunk[0].name == "retrieve":
                        data = ""
                        
                    # 调用工具生成图片
                    if chunk[0].name == "create_image":
                        img_data = chunk[0].artifact[0].data
                        img_mime = chunk[0].artifact[0].mimeType
                        j_c = json.dumps({"v":f"data:{img_mime};base64,{img_data}"}, ensure_ascii=False)
                        data = f'event:image\ndata: {j_c}\n\n'
                        pre_event = cur_event
                        cur_event = "image"
                        
                elif type(chunk[0]) == AIMessageChunk or type(chunk[0]) == AIMessage:
                    if chunk[1]['langgraph_node'] in ['grade_documents', "rewrite", "router_content"]: 
                        data = ""
                        
                    if chunk[1]['langgraph_node'] in ['assistant', 'agent'] and c != "":
                        j_c = json.dumps({"v":c}, ensure_ascii=False)
                        data = f'event:think\ndata: {j_c}\n\n'
                        pre_event = cur_event
                        cur_event = "think"
                    
                    if chunk[1]['langgraph_node'] in ['generate'] and c != "":
                        j_c = json.dumps({"v":c}, ensure_ascii=False)
                        data = f'event:message\ndata: {j_c}\n\n'
                        pre_event = cur_event
                        cur_event = "message"
                        
                    if chunk[1]['langgraph_node'] == "recommend_question" and c != "":
                        cs = c.split("\n")
                        if len(cs) == 2:
                            if re.search(r"\bI\b", cs[0]):
                                pre_event = cur_event 
                                yield f"event:{pre_event}-end\ndata: {{}}\n\n"
                                tmp = cs[0].replace("I", "").strip().strip(".")
                                j_c = json.dumps({"v":tmp}, ensure_ascii=False)
                                data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                                yield data
                                yield f"event:recommend{rec_seq}-end\ndata: {{}}\n\n"
                                rec_seq = rec_seq + 1
                                cur_event = f"recommend{rec_seq}"
                                pre_event = cur_event
                                data = ""
                            else:
                                j_c = json.dumps({"v":cs[0]}, ensure_ascii=False)
                                data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                                yield data
                                yield f"event:{pre_event}-end\ndata: {{}}\n\n"
                                data = ""
                                rec_seq = rec_seq + 1
                                cur_event = f"recommend{rec_seq}"
                                pre_event = cur_event

                            if re.search(r"\bII\b", cs[1]):
                                tmp = cs[1].replace("II", "").strip().strip(".")
                                j_c = json.dumps({"v":tmp}, ensure_ascii=False)
                                data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                            if  re.search(r"\bIII\b", cs[1]):
                                tmp = cs[1].replace("III", "").strip().strip(".")
                                j_c = json.dumps({"v":tmp}, ensure_ascii=False)
                                data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                        else:
                            if re.search(r"\bI\b", c):
                                rec_seq = 1
                                c = c.replace("I", "").strip().strip(".")
                            if  re.search(r"\bII\b", c):
                                rec_seq = 2
                                c = c.replace("II", "").strip().strip(".")
                            if re.search(r"\bIII\b", c):
                                rec_seq = 3
                                c = c.replace("III", "").strip().strip(".")
                            c = c.strip().strip(".")
                            j_c = json.dumps({"v":c}, ensure_ascii=False)
                            data = f'event:recommend{rec_seq}\ndata: {j_c}\n\n'
                            pre_event = cur_event
                            cur_event = f"recommend{rec_seq}"
                        
                if pre_event == "":
                    pre_event = cur_event
                                
                if data != "":
                    if cur_event != pre_event:
                        yield f"event:{pre_event}-end\ndata: {{}}\n\n"
                    yield data
                        
            c = json.dumps({"done":True})
            yield f"event:{pre_event}-end\ndata: {{}}\n\n"
            yield f'data: {c}\n\n'
            
        except Exception as e:
            root_logger.error(f"Streaming error: {traceback.format_exc()}")
            c = {"error":"小助手开小差了，换个问题试试吧。"}
            yield  f'data: {c}\n\n'
                          
@router.post("", summary="LLM chat interface")
async def llm_endpoint(request:Request,  data: LLMRequest = Body()):
    """
    LLM endpoint for question answering with RAG (using LangGraph).
    """
    
    try:
        # 1. Extract parameters from request
        chat_history = data.chatHistory
        config = data.config
        context = data.context
        datasource = data.datasource
        embeddings = data.embeddings
        message = data.message
        image = data.image
        user_id = data.userId
        assistant_id = data.assistantId
        search = data.search
        tenant_id = data.tenantId
        rag = message and (embeddings or datasource)
        
        # 准备retriever数据
        model = init_llm_model(config)
        thread_config = {"configurable": {"thread_id": f"{user_id}_{assistant_id}"}, "user_id":user_id}

        if assistant_id == "-1":
            g = event_graphiti(request, message, context, chat_history,  model, tenant_id, user_id, thread_config)
        else:     
            g = event_generator(request, message, context, chat_history, image, model, datasource, embeddings, tenant_id, user_id, search, rag, thread_config)

        # g = event_generator(request, message, context, chat_history, image, model, datasource, embeddings, tenant_id, user_id, search, rag, thread_config)

        return StreamingResponse(
            g,
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"  # Disable buffering in Nginx
            }
        )

    except Exception as e:
        root_logger.error(f"Error invoke LLM Endpoint: {traceback.format_exc()}")
        root_logger.error(traceback.format_exc())
        
        g = error_generator(request)
        return StreamingResponse(
            g,
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"  # Disable buffering in Nginx
            }
        )
