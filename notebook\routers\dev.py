#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import datetime

from fastapi import APIRouter, Form, Query, Request
from ..utils import RespCode

router = APIRouter(prefix='/dev', tags=['开发调试用接口'])

@router.get('/ping', summary='ping')
def ping():
    return RespCode.resp_ok({"msg": "pong"})

@router.get('/now', summary='time')
def now():
    return RespCode.resp_ok({"now": datetime.datetime.now()})

@router.get('/ip', summary='get real ip')
def ipip(request: Request):
    hostip = request.client.host
    realip = request.headers.get("x_real_ip")
    forwardip = request.headers.get("x_forwarded_for")
    return RespCode.resp_ok({"ip": hostip, "realip" : realip, "forwardip" : forwardip})

