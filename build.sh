#!/bin/bash
git pull
data_date=$(date +%Y%m%d)
#docker build -f docker/Dockerfile . -t bluestar:${data_date}
#docker tag bluestar:${data_date} bluestar:latest

#docker-compose -f docker/docker-compose.yml down
#docker compose -f docker/docker-compose.yml up
docker cp . bluestar-api:/app
docker exec bluestar-api pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple -r /app/requirements.txt
docker restart bluestar-api
