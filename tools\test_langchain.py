#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import os
import sys
import argparse

# Add the parent directory to the path so we can import from notebook
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set proxy if needed
os.environ["HTTP_PROXY"] = "http://10.9.0.251:1080"
os.environ["HTTPS_PROXY"] = "http://10.9.0.251:1080"

from notebook.libs.langchain_lib import LangChainService
from langchain.docstore.document import Document

def test_add_and_query():
    """Test adding documents and querying with LangChain"""
    try:
        # Initialize the LangChain service
        langchain_service = LangChainService(tenant_id = "banyunjuhe", database_name="embeddings")
        
        # Create a test collection
        collection_name = "test_collection"
        
        # Delete the collection if it exists and recreate it
        try:
            langchain_service.chroma_client.delete_collection(collection_name)
            print(f"Deleted existing collection: {collection_name}")
        except Exception as e:
            print(f"Collection {collection_name} did not exist")
            
        # Create fresh collection
        langchain_service.get_or_create_collection(collection_name)
        print(f"Created new collection: {collection_name}")
        # Create some test documents
        documents = [
            Document(
                page_content="LangChain is a framework for developing applications powered by language models.",
                metadata={"source": "test"}
            ),
            Document(
                page_content="LangChain provides many modules that can be used to build language model applications.",
                metadata={"source": "test"}
            ),
            Document(
                page_content="LangChain supports integration with various vector stores and databases.",
                metadata={"source": "test"}
            )
        ]
        
        # Add documents to the vector store
        print("Adding documents to the vector store...")
        langchain_service.add_documents_with_metadata(documents, collection_name=collection_name)
        
        # Query the vector store
        print("Querying the vector store...")
        query_text = "What is LangChain used for?"
        results = langchain_service.query(query_text, collection_name=collection_name)
        
        print(f"Query: {query_text}")
        print("Results:")
        for i,  doc in enumerate(results):
            print(f"\n{i}. Content: {doc[0].page_content}")
            print(f"   Metadata: {doc[0].metadata}")
            print(f"   Score: {doc[1]}")
        
        return True
    except Exception as e:
        print(f"Error in test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_query(tenant_id, database, collection_name, query):
    """Test querying with LangChain"""
    try:
        # Initialize the LangChain service
        langchain_service = LangChainService(tenant_id=tenant_id, database_name=database)

        # Query the vector store
        print("Querying the vector store...")
        results = langchain_service.query(query, collection_name=collection_name)

        print(f"Query: {query}")
        print("Results:")
        for i, doc in enumerate(results):
            print(f"\n{i}. Content: {doc[0].page_content}")
            print(f"   Metadata: {doc[0].metadata}")
            print(f"   Score: {doc[1]}")

        return True
    except Exception as e:
        print(f"Error in test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def parse_args():
    desc = "Test LangChain implementation"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--add', action='store_true', help='Run test_add_and_query')
    parser.add_argument('-t', '--tenant_id', type=str, default="banyunjuhe", help='Tenant ID')
    parser.add_argument('-d', '--database', type=str, default="database", help='Database name')
    parser.add_argument('-c', '--collection_name', type=str, help='Collection name')
    parser.add_argument('-q', '--query', type=str, help='Query string')
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_args()

    print("Testing LangChain implementation...")

    if args.add:
        success = test_add_and_query()
        if success:
            print("Test completed successfully!")
        else:
            print("Test failed!")
    else:
        if not all([args.tenant_id, args.database, args.collection_name, args.query]):
            print("Error: tenant_id, database, collection_name, and query are required.")
        else:
            success = test_query(args.tenant_id, args.database, args.collection_name, args.query)
            if success:
                print("Test completed successfully!")
            else:
                print("Test failed!")
