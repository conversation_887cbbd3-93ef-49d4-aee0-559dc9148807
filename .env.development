DEBUG=true
# Your openai api key. (required)
OPENAI_API_KEY=********************************************************************************************************************************************************************
QWEN_API_KEY=sk-9502dc0b92784939b2f1986d897a41ea
OPENROUTER_API_KEY=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
GOOGLE_API_KEY=AIzaSyCZf2cxrvhOewsDRjXQ8i_h3gXluIIXIrc

OLLAMA_BASE_URL="http://**********:11434"
DEEPSEEK_MODEL_QUANT=":7b"

HTTP_PROXY=http://**********:1080
HTTPS_PROXY=http://**********:1080

# Name of the embedding model to use.
EMBEDDING_MODEL=text-embedding-3-large
# Dimension of the embedding model to use.
EMBEDDING_DIM=1536

CHROMA_SERVER_HOST="**********"
CHROMA_SERVER_PORT="8000"
CHROMA_SERVER_AUTHN_CREDENTIALS="admin:Smile5More#apper1"
CHROMA_SERVER_AUTHN_PROVIDER="chromadb.auth.basic_authn.BasicAuthClientProvider"

POSTGRES_DB_URI="*********************************************************************"

# gpt-5-mini需要验证才能返回流式输出；
OPENAI_MODELS="gpt-4o-mini,o1-mini,o3-mini,o4-mini,gpt-4.1-mini,gpt-4.1-nano,gpt-5-nano,gpt-5-mini"
QWEN_MODELS="deepseek-r1,deepseek-v3,qwen-max-latest,qwen-turbo-latest,qwen-plus-latest,qwen-plus,qwen-turbo,qwen-max"
OPENROUTER_MODELS="deepseek/deepseek-r1-0528-qwen3-8b:free,mistralai/mistral-small-3.1-24b-instruct:free"
GOOGLE_MODELS="gemini-2.0-flash-lite,gemini-2.0-flash,gemini-2.5-flash-preview-05-20"

TAVILY_API_KEY="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"

# LANGSMITH_TRACING=true
# LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
# LANGSMITH_API_KEY="***************************************************"
# LANGSMITH_PROJECT="api-llamaindex"

OSS_ENDPOINT="oss-rg-china-mainland.aliyuncs.com"
OSS_SECRETKEY="LTAI5tBWs1EknMeactnaxhHK"
OSS_SECRETPASS="******************************"
OSS_BUCKET="byjh-ai-data"

OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"
QWEN_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"
OPENAI_BASE_URL="https://api.openai.com/v1"
GOOGLE_BASE_URL="https://generativelanguage.googleapis.com/v1beta/openai/"

PYTHON_CMD="./.venv/Scripts/python"
MCP_HOST="************"
NO_PROXY="************"

NEO4J_URI="bolt://************:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="neo4j_data"

QWEN_EMBEDDING_MODEL="text-embedding-v4"
QWEN_EMBEDDING_DIM="1536"


