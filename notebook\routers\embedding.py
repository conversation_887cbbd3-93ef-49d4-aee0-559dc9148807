#!/usr/bin/env python
# -*- coding:utf-8 -*-

from fastapi import APIRouter, Body
from ..libs.langchain_lib import LangChainService
from ..schemas.embedding import Embedding<PERSON>son, EmbeddingSource, EmbeddingQuery, EmbeddingEntity
from ..utils import RespCode, hash_tool
from ..utils.content_helper import * 
import logging
from langchain.docstore.document import Document
import traceback

root_logger = logging.getLogger("root")
router = APIRouter(prefix='/embedding', tags=['创建embedding接口'])

@router.post("", summary="从PDF、文本或URL中创建embedding")
def create_embedding(
    data: EmbeddingSource = Body(...)
):
    try:
        pdf = data.pdf
        text = data.text
        url = data.url
        excel = data.excel
        dsName = data.dsName
        userId = data.userId

        # Validate input - ensure at least one source is provided
        if not any([pdf, text, url, excel]):
            return RespCode.resp_err("At least one of pdf, text, or url must be provided")

        content = ""
        
        # Extract content based on provided source
        if pdf:
            content = extract_text_from_pdf(pdf)
        elif text:
            content = text
        elif url:
            content = get_url_content(url)
        elif excel:
            content = extract_text_from_excel(excel)
            
        if not content.strip():
            return RespCode.resp_err("No content could be extracted from the provided source")

        root_logger.info(f"try create embedding for {dsName} with content : {content}")

        md5id = compute_md5(content)
        # Create document with metadata
        document = Document(
            page_content=content,
            metadata={
                "userId": userId,
                "fileName": dsName,
                "md5id":md5id
            }
        )

        # Initialize LangChain service
        tenant_id = "banyunjuhe"
        database_name = "embeddings"
        collection_name = f"{userId}_em_{compute_md5(dsName)}"

        # Create service and add document
        langchain_service = LangChainService(tenant_id, database_name)
        try:
            collection = langchain_service.get_or_create_collection(collection_name)
            existData = collection.get(where={"userId": {"$eq": userId}})
            collection.delete(ids=existData["ids"])
        except Exception as exp:
            pass

        # Add document to vector store
        langchain_service.add_documents_with_metadata([document], collection_name=collection_name)
        
        # Log success
        root_logger.info(f"Successfully created embedding for {dsName}")
        
        return RespCode.resp_ok({
            'msg': "Embedding created successfully",
            'embeddingName': collection_name,
            'tenant': tenant_id,
            'database': database_name
        })

    except Exception as e:
        root_logger.error(f"Error creating embedding: {str(e)}")
        root_logger.error(traceback.format_exc())
        return RespCode.resp_err(f"Error creating embedding: {str(e)}")

@router.post("/json", summary="同步embedding历史")
def sync_embedding(data: EmbeddingJson = Body(...)):
    try:
        # Create tenant and database if they don't exist
        tenant_id = "banyunjuhe"
        database_name = "embeddings"
        file_name = "history_" + data.assistantId


        # Create documents from the embeddings data
        documents = []
        for item in data.embeddings:
            md5id = compute_md5(item["text"])
            document = Document(
                page_content=item["text"],
                metadata={
                    "userId": data.userId,
                    "fileName": file_name,
                    "md5id":md5id,
                }
            )
            documents.append(document)

        # Generate collection name using userId and MD5 hash of "history"
        collection_name = f"{data.userId}_em_{compute_md5(file_name)}"

        # Initialize services
        langchain_service = LangChainService(tenant_id, database_name)
        try:
            collection = langchain_service.get_or_create_collection(collection_name)
            existData = collection.get(where={"userId": {"$eq": data.userId}})
            collection.delete(ids=existData["ids"])
        except Exception as exp:
            pass

        # Add documents to the vector store
        langchain_service.add_documents_with_metadata(documents, collection_name=collection_name)
        
        # Log the collections for debugging
        root_logger.info(f"Collections: {langchain_service.chroma_client.list_collections()}")
        
        return RespCode.resp_ok({
            'msg': "Documents added successfully",
            'embeddingName': collection_name,
            'tenant': tenant_id,
            'database': database_name
        })
    except Exception as e:
        root_logger.error(f"Error adding document: {str(e)}")
        root_logger.error(traceback.format_exc())
        return RespCode.resp_err(f"Error adding document: {str(e)}")

@router.post("/query", summary="查询embedding")
def query_embedding(data:EmbeddingQuery = Body(...)):
    try:

        tenant_id = "banyunjuhe"
        database_name = "embeddings"
        userId = data.userId
        fileName = data.dsName
        text = data.text 

        # Generate collection name
        collection_name = f"{userId}_em_{compute_md5(fileName)}"
        # Initialize services and set constants
        langchain_service = LangChainService(tenant_id, database_name)
        root_logger.info(f"Querying collection: {collection_name} in tenant: {tenant_id}, database: {database_name}")
        
        try:
            # Use LangChain to query the vector store with all required parameters
            results = langchain_service.query(
                query_text=text,
                collection_name=collection_name
            )
            
            # Format the results
            formatted_results = [
                {
                    'content': doc[0].page_content,
                    'metadata': doc[0].metadata,
                    'score': float(doc[1])  # Convert numpy float to Python float
                }
                for doc in results
            ]
            
            return RespCode.resp_ok({
                'results': formatted_results,
                'collection_info': {
                    'collection_name': collection_name,
                    'tenant': tenant_id,
                    'database': database_name
                }
            })
        except Exception as e:
            # If collection doesn't exist or is empty, return empty results
            root_logger.warning(f"Error querying collection: {str(e)}")
            return RespCode.resp_ok({
                'results': [],
                'collection_info': {
                    'collection_name': collection_name,
                    'tenant': tenant_id,
                    'database': database_name
                },
                'warning': f"Collection may not exist or is empty: {str(e)}"
            })
            
    except Exception as e:
        root_logger.error(f"Error in query endpoint: {str(e)}")
        root_logger.error(traceback.format_exc())
        return RespCode.resp_err(f"Error in query endpoint: {str(e)}")

@router.post("/delete")
def delete_collection(data: EmbeddingEntity = Body(...)):
    try:
        # Initialize constants
        tenant_id = "banyunjuhe"
        database_name = "embeddings"
        fileName = data.dsName
        userId = data.userId
        
        # Generate collection name
        collection_name = f"{userId}_em_{compute_md5(fileName)}"
        
        # Initialize LangChain service
        langchain_service = LangChainService(tenant_id, database_name)
        root_logger.info(f"Deleting collection: {collection_name} in tenant: {tenant_id}, database: {database_name}")
        
        try:
            # Get the collection and delete it
            collection = langchain_service.get_or_create_collection(collection_name)
            data = collection.get(
                where={"userId": {"$eq": userId}}
            )
            collection.delete(ids=data["ids"])
            return RespCode.resp_ok({
                'msg': 'Collection deleted successfully',
                'collection_info': {
                    'collection_name': collection_name,
                    'tenant': tenant_id,
                    'database': database_name
                }
            })
            
        except Exception as e:
            root_logger.warning(f"Error deleting collection: {str(e)}")
            return RespCode.resp_ok({
                'msg': 'Collection may not exist',
                'collection_info': {
                    'collection_name': collection_name,
                    'tenant': tenant_id,
                    'database': database_name
                },
                'warning': f"Error deleting collection: {str(e)}"
            })
            
    except Exception as e:
        root_logger.error(f"Error in delete endpoint: {str(e)}")
        root_logger.error(traceback.format_exc())
        return RespCode.resp_err(f"Error in delete endpoint: {str(e)}")
    
@router.post("/list")
def list_collection(data: EmbeddingEntity = Body(...)):
    try:
        # Initialize constants
        tenant_id = "banyunjuhe"
        database_name = "embeddings"
        fileName = data.dsName
        userId = data.userId 
        # Generate collection name
        collection_name = f"{userId}_em_{compute_md5(fileName)}"
        
        # Initialize LangChain service
        langchain_service = LangChainService(tenant_id, database_name)
        root_logger.info(f"Listing collection: {collection_name} in tenant: {tenant_id}, database: {database_name}")
        
        try:
            # Get the collection and list all documents
            collection = langchain_service.get_or_create_collection(collection_name)
            data = collection.get(
                where={"userId": {"$eq": userId}}
            )
            
            return RespCode.resp_ok({
                'msg': 'Collection listed successfully',
                'collection_info': {
                    'collection_name': collection_name,
                    'tenant': tenant_id,
                    'database': database_name
                },
                'data': {
                    'ids': data['ids'],
                    'documents': data['documents'],
                    'metadatas': data['metadatas']
                }
            })
            
        except Exception as e:
            root_logger.warning(f"Error listing collection: {str(e)}")
            return RespCode.resp_ok({
                'msg': 'Collection may not exist',
                'collection_info': {
                    'collection_name': collection_name,
                    'tenant': tenant_id,
                    'database': database_name
                },
                'warning': f"Error listing collection: {str(e)}"
            })
            
    except Exception as e:
        root_logger.error(f"Error in list endpoint: {str(e)}")
        root_logger.error(traceback.format_exc())
        return RespCode.resp_err(f"Error in list endpoint: {str(e)}")
