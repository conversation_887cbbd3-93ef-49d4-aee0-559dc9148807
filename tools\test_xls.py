import pandas as pd

def extract_text_from_excel(excel_file: str) -> str:
    """Extract CSV content from an Excel file."""
    try:

        # Read all sheets from the Excel file
        xls = pd.ExcelFile(excel_file, engine="xlrd")
        
        csv_content = ""
        for sheet_name in xls.sheet_names:
            df = xls.parse(sheet_name)
            csv_content += df.to_csv(index=False, sep="\t") + "\n"
        
        return csv_content.strip()
    
    except Exception as e:
    
        raise e


text = extract_text_from_excel("D:/hexueying/temp/test.xls")
print(text)
