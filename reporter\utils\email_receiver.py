import logging
import imaplib
import email
from datetime import datetime, timedelta
from typing import List, Dict, Any
from email.header import decode_header
import ssl

logger = logging.getLogger(__name__)

class EmailReceiver:
    def __init__(self, email_address: str, password: str, imap_server: str = "imap.exmail.qq.com", port: int = 993):
        """
        初始化邮件接收器
        
        Args:
            email_address: 邮箱地址
            password: 邮箱密码或应用专用密码
            imap_server: IMAP服务器地址
            port: IMAP端口
        """
        self.email_address = email_address
        self.password = password
        self.imap_server = imap_server
        self.port = port
        self.mail = None
        logger.info(f"EmailReceiver initialized for {email_address}")

    def connect(self) -> bool:
        """连接到邮箱服务器"""
        try:
            # 创建SSL上下文
            context = ssl.create_default_context()
            self.mail = imaplib.IMAP4_SSL(self.imap_server, self.port, ssl_context=context)
            self.mail.login(self.email_address, self.password)
            logger.info(f"Successfully connected to {self.imap_server}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to email server: {str(e)}")
            return False

    def disconnect(self):
        """断开邮箱连接"""
        if self.mail:
            try:
                self.mail.close()
                self.mail.logout()
                logger.info("Disconnected from email server")
            except Exception as e:
                logger.error(f"Error disconnecting from email server: {str(e)}")

    def _decode_header(self, header_value: str) -> str:
        """解码邮件头部信息"""
        if header_value is None:
            return ""
        
        decoded_parts = decode_header(header_value)
        decoded_string = ""
        
        for part, encoding in decoded_parts:
            if isinstance(part, bytes):
                if encoding:
                    decoded_string += part.decode(encoding)
                else:
                    decoded_string += part.decode('utf-8', errors='ignore')
            else:
                decoded_string += part
        
        return decoded_string

    def _get_email_content(self, msg) -> Dict[str, Any]:
        """提取邮件内容"""
        content = {
            'text': '',
            'html': '',
            'attachments': []
        }
        
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if "attachment" not in content_disposition:
                    if content_type == "text/plain":
                        body = part.get_payload(decode=True)
                        if body:
                            content['text'] += body.decode('utf-8', errors='ignore')
                    elif content_type == "text/html":
                        body = part.get_payload(decode=True)
                        if body:
                            content['html'] += body.decode('utf-8', errors='ignore')
                else:
                    # 处理附件
                    filename = part.get_filename()
                    if filename:
                        content['attachments'].append({
                            'filename': self._decode_header(filename),
                            'content_type': content_type,
                            'size': len(part.get_payload(decode=True) or b'')
                        })
        else:
            content_type = msg.get_content_type()
            body = msg.get_payload(decode=True)
            if body:
                if content_type == "text/plain":
                    content['text'] = body.decode('utf-8', errors='ignore')
                elif content_type == "text/html":
                    content['html'] = body.decode('utf-8', errors='ignore')
        
        return content

    def get_yesterday_emails(self) -> List[Dict[str, Any]]:
        """获取昨天收到的邮件"""
        if not self.connect():
            return []
        
        try:
            # 选择收件箱
            self.mail.select('INBOX')
            
            # 计算昨天的日期
            yesterday = datetime.now() - timedelta(days=1)
            date_str = yesterday.strftime("%d-%b-%Y")
            
            # 搜索昨天的邮件
            search_criteria = f'(ON "{date_str}")'
            logger.info(f"Searching emails with criteria: {search_criteria}")
            
            status, messages = self.mail.search(None, search_criteria)
            
            if status != 'OK':
                logger.error(f"Failed to search emails: {status}")
                return []
            
            email_ids = messages[0].split()
            logger.info(f"Found {len(email_ids)} emails from yesterday")
            
            emails = []
            
            for email_id in email_ids:
                try:
                    # 获取邮件
                    status, msg_data = self.mail.fetch(email_id, '(RFC822)')
                    
                    if status != 'OK':
                        logger.warning(f"Failed to fetch email {email_id}")
                        continue
                    
                    # 解析邮件
                    raw_email = msg_data[0][1]
                    msg = email.message_from_bytes(raw_email)
                    
                    content = self._get_email_content(msg)
                    # 提取邮件信息
                    email_info = {
                        'id': email_id.decode(),
                        'subject': self._decode_header(msg.get('Subject', '')),
                        'from': self._decode_header(msg.get('From', '')),
                        'to': self._decode_header(msg.get('To', '')),
                        'date': msg.get('Date', ''),
                        'content': content["text"] if "text" in content else ""
                    }
                    
                    emails.append(email_info)
                    logger.debug(f"Processed email: {email_info['subject']}")
                    
                except Exception as e:
                    logger.error(f"Error processing email {email_id}: {str(e)}")
                    continue
            
            logger.info(f"Successfully retrieved {len(emails)} emails")
            return emails
            
        except Exception as e:
            logger.error(f"Error retrieving emails: {str(e)}")
            return []
        finally:
            self.disconnect()

    def get_emails_by_date_range(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """根据日期范围获取邮件"""
        if not self.connect():
            return []
        
        try:
            self.mail.select('INBOX')
            
            start_str = start_date.strftime("%d-%b-%Y")
            end_str = end_date.strftime("%d-%b-%Y")
            
            search_criteria = f'(SINCE "{start_str}" BEFORE "{end_str}")'
            logger.info(f"Searching emails from {start_str} to {end_str}")
            
            status, messages = self.mail.search(None, search_criteria)
            
            if status != 'OK':
                logger.error(f"Failed to search emails: {status}")
                return []
            
            email_ids = messages[0].split()
            logger.info(f"Found {len(email_ids)} emails in date range")
            
            emails = []
            for email_id in email_ids:
                try:
                    status, msg_data = self.mail.fetch(email_id, '(RFC822)')
                    if status == 'OK':
                        raw_email = msg_data[0][1]
                        msg = email.message_from_bytes(raw_email)
                        content = self._get_email_content(msg)
                        email_info = {
                            'id': email_id.decode(),
                            'subject': self._decode_header(msg.get('Subject', '')),
                            'from': self._decode_header(msg.get('From', '')),
                            'to': self._decode_header(msg.get('To', '')),
                            'date': msg.get('Date', ''),
                            'content': content["text"] if "text" in content else ""
                        }
                        
                        emails.append(email_info)
                        
                except Exception as e:
                    logger.error(f"Error processing email {email_id}: {str(e)}")
                    continue
            
            return emails
            
        except Exception as e:
            logger.error(f"Error retrieving emails by date range: {str(e)}")
            return []
        finally:
            self.disconnect()

if __name__ == "__main__":
    # 测试
    receiver = EmailReceiver(
        email_address="<EMAIL>",
        password="agent@2025",
        imap_server="imap.qiye.aliyun.com",  # 企业微信邮箱IMAP服务器
        port=993
    )

    emails = receiver.get_yesterday_emails()
    print(emails)

