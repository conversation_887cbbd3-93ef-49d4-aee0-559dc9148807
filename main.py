#!/usr/bin/env python
# -*- coding:utf-8 -*-

import uvicorn
from notebook.config import get_settings
import traceback
import asyncio
import sys

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
if __name__ == '__main__':
    settings = get_settings()
    try:
        uvicorn.run("notebook.server:app",
                    host=settings.server_host,
                    port=settings.server_port,
                    workers= 1 if settings.profile == "DEV" else 4, 
                    reload=settings.debug,
                    proxy_headers=True,
                    forwarded_allow_ips="*",
                    log_level="info",
                    reload_dirs=["./notebook"] if settings.debug else None) 
    except Exception as e:
        print(f"An error occurred: {traceback.format_exc()}")
