#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from pydantic import BaseModel, Field, field_validator
from typing import Optional

class EmbeddingJson(BaseModel):
    userId: str = Field(..., example="admin")
    assistantId: str = Field(..., example="1")
    embeddings: list = Field(..., example=[
        {
            "text": "hello",
            "embedding": [0.1, 0.2, 0.3]
        }
    ])
    
    @field_validator('embeddings')
    def validate_embedding(cls, v):
        if not isinstance(v, list):
            raise ValueError('embedding must be a list')
        for item in v:
            if not isinstance(item, dict):
                raise ValueError('each embedding item must be a dict')
            if 'text' not in item or 'embedding' not in item:
                raise ValueError('each embedding item must have text and embedding fields')
            if not isinstance(item['text'], str):
                raise ValueError('text field must be a string')
            if not isinstance(item['embedding'], list):
                raise ValueError('embedding field must be a list')
            if not all(isinstance(x, float) for x in item['embedding']):
                raise ValueError('embedding values must be floats')
        return v


class EmbeddingSource(BaseModel):
    userId: str = Field(..., example="admin")
    dsName: str = Field(..., example="history")
    pdf: Optional[str] =  Field(None)
    text: Optional[str] = Field(None)
    url: Optional[str] =  Field(None)
    excel: Optional[str] =  Field(None)

class EmbeddingQuery(BaseModel):
    userId: str = Field(..., example="admin")
    dsName: str = Field(..., example="history")
    text: str = Field(..., example="text")
    
class EmbeddingEntity(BaseModel):
    userId: str = Field(..., example="admin")
    dsName: str = Field(..., example="history")