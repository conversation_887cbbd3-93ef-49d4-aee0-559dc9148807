# report_generators/markdown_generator.py
import pandas as pd
import json
import os
from datetime import datetime
import glob
from utils.oss_uploader import upload_oss



class MarkdownGenerator:
    def __init__(self, data_path="data"):
        """
        初始化Markdown报告生成器
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
    
    def _load_all_data(self):
        """
        加载所有相关数据
        
        Returns:
            包含所有数据的字典
        """
        data = {}
        
        # 定义需要加载的数据文件
        data_files = [
            "ipct_day_data",
            "ipct_gen_image", 
            "heli_income_sum",
            "heli_day_data",
            "heli_hebao_active",
            "heli_hebao_policy",
            "conversion_day_data"
        ]
        
        # 加载所有数据文件
        for file_name in data_files:
            try:
                file_path = f"{self.data_path}/{file_name}.json"
                if os.path.exists(file_path):
                    with open(file_path, "r", encoding="utf-8") as f:
                        data[file_name] = json.load(f)
                else:
                    data[file_name] = []
            except Exception as e:
                print(f"Warning: Failed to load {file_name}: {e}")
                data[file_name] = []
                
        return data
    
    def _process_data(self, raw_data):
        """
        处理原始数据，转换为报告所需格式
        
        Args:
            raw_data: 从JSON文件加载的原始数据
            
        Returns:
            处理后的数据字典
        """
        processed_data = {}
        
        # 处理爱拼长图DAU数据
        if "ipct_day_data" in raw_data and raw_data["ipct_day_data"]:
            df_ipct_day = pd.DataFrame(raw_data["ipct_day_data"]) 
            df_ipct_day.rename(columns={"data_date": "日期", "uv": "活跃用户", "pay_user": "付费用户"}, inplace=True)
            df_ipct_day["日期"] = pd.to_datetime(df_ipct_day["日期"]).dt.strftime('%Y-%m-%d')
            df_ipct_day.sort_values(by="日期", ascending=False, inplace=True)
            processed_data["aiping_dau"] = df_ipct_day
            
        # 处理爱拼长图生图数据
        if "ipct_gen_image" in raw_data and raw_data["ipct_gen_image"]:
            df_ipct_gen = pd.DataFrame(raw_data["ipct_gen_image"])
            df_ipct_gen.rename(columns={"data_date": "日期", "channel": "渠道", "num": "生图次数", "uv": "生图用户"}, inplace=True)
            df_ipct_gen["日期"] = pd.to_datetime(df_ipct_gen["日期"]).dt.strftime('%Y-%m-%d')
            df_ipct_gen.sort_values(by="日期", ascending=False, inplace=True)
            processed_data["aiping_gen_image_data"] = df_ipct_gen
            
        # 处理赫鲤收入数据（按天展示）
        if "heli_income_sum" in raw_data and raw_data["heli_income_sum"]:
            df_heli_income = pd.DataFrame(raw_data["heli_income_sum"])
            df_heli_income.rename(columns={"data_date": "日期", "income": "收入"}, inplace=True)
            df_heli_income["日期"] = pd.to_datetime(df_heli_income["日期"]).dt.strftime('%Y-%m-%d')
            df_heli_income.sort_values(by="日期", ascending=False, inplace=True)
            processed_data["heli_income_by_day"] = df_heli_income
            
        # 处理赫鲤日数据
        if "heli_day_data" in raw_data and raw_data["heli_day_data"]:
            df_heli_day = pd.DataFrame(raw_data["heli_day_data"])
            # 按日期和渠道分组聚合数据
            df_channel_metrics = df_heli_day.groupby(['data_date', 'qudao']).agg({
                'boot_uv': 'sum',
                'login_suc_uv': 'sum',
                'pay_suc_uv': 'sum'
            }).reset_index()
            df_channel_metrics.rename(columns={"data_date": "日期", "qudao": "渠道", "boot_uv": "启动用户", "login_suc_uv": "登录成功用户", "pay_suc_uv": "付费用户"}, inplace=True)
            df_channel_metrics["日期"] = pd.to_datetime(df_channel_metrics["日期"]).dt.strftime('%Y-%m-%d')
            df_channel_metrics.sort_values(by="日期", ascending=False, inplace=True)
            processed_data["heli_channel_metrics"] = df_channel_metrics
            
        # 处理赫宝活跃数据
        if "heli_hebao_active" in raw_data and raw_data["heli_hebao_active"]:
            df_heli_hebao = pd.DataFrame(raw_data["heli_hebao_active"])
            df_heli_hebao.rename(columns={"app_type": "应用类型", "name": "应用名称", "landing_type": "着陆页类型", "mat_type": "物料类型"}, inplace=True)
            processed_data["heli_hebao_metrics"] = df_heli_hebao
            
        # 处理赫宝策略数据
        if "heli_hebao_policy" in raw_data and raw_data["heli_hebao_policy"]:
            df_heli_policy = pd.DataFrame(raw_data["heli_hebao_policy"])
            df_heli_policy.rename(columns={"id": "策略ID", "name": "策略名称", "orig_price": "原价", "cur_price": "现价"}, inplace=True)
            processed_data["heli_hebao_policy"] = df_heli_policy
            
        # 处理转化漏斗数据
        if "conversion_day_data" in raw_data and raw_data["conversion_day_data"]:
            df_conversion = pd.DataFrame(raw_data["conversion_day_data"])
            # 重命名列以匹配中文显示
            df_conversion.rename(columns={
                "data_date": "日期",
                "boot_uv": "启动用户",
                "privacy_uv": "隐私页用户",
                "hebao_uv": "荷包页用户",
                "login_uv": "登录页用户",
                "login_suc_uv": "登录成功用户",
                "pay_uv": "付费页用户",
                "confirmpay_uv": "确认付费用户",
                "pay_suc_uv": "付费成功用户"
            }, inplace=True)
            df_conversion["日期"] = pd.to_datetime(df_conversion["日期"]).dt.strftime('%Y-%m-%d')
            processed_data["conversion_data"] = df_conversion
            
        return processed_data
    
    def _upload_to_oss(self):
        """
        上传文件到OSS
        """
        #读取data_path/charts下所有图片，并上传, 返回上传后的CDN URL字典
        chart_files = glob.glob(f"{self.data_path}/charts/*.png")
        if not chart_files:
            print("No chart files found to upload.")
            return
        
        # 定义一个空字典来存储CDN URL
        cdn_urls = {}
        
        for chart_file in chart_files:
            # 获取文件名（不含路径）
            chart_name = os.path.basename(chart_file) 
            _, ymd = chart_file.split("/")[:2] 
            year = ymd[0:4] # 获取年月日
            month = ymd[4:6]
            day = ymd[6:8]

            # 上传图表到OSS并获取CDN URL
            cdn_url = upload_oss(chart_file, year, month, day)
            if cdn_url:
                # 如果上传成功，使用CDN URL
                cdn_urls[chart_name] = cdn_url

        return cdn_urls

    def generate_markdown(self, data_date: datetime) -> str:
        """
        根据从JSON文件读取的数据生成Markdown格式的总结报告。
        """
        print("Generating Markdown report...")
        
        # 加载所有数据
        raw_data = self._load_all_data()
        
        # 处理数据
        report_data = self._process_data(raw_data)
        
        cdn_urls = self._upload_to_oss()
        markdown_content = f"# 运营数据日报 - {data_date.strftime('%Y年%m月%d日')}\n\n"

        # 市场情况总结
        markdown_content += "## 市场情况\n"
        markdown_content += "> 暂无应用上下架情况数据。\n"
        markdown_content += "\n"

        # 经营指标 - 爱拼长图
        markdown_content += "## 爱拼长图 - 近七天经营指标\n"
        if "aiping_dau" not in report_data or report_data["aiping_dau"].empty:
            markdown_content += "> 暂无用户活跃数据。\n"
        else:
            markdown_content += "### 用户活跃数据\n"
            # 加载图
            if "aiping_dau_trend.png" in cdn_urls:
                markdown_content += f"![爱拼长图DAU趋势图]({cdn_urls['aiping_dau_trend.png']})\n\n"
            # 加载表格  
            markdown_content += report_data["aiping_dau"].to_markdown(index=False) + "\n\n"



        if "aiping_gen_image_data" not in report_data or report_data["aiping_gen_image_data"].empty:
            markdown_content += "> 暂无生图次数&生图用户数数据。\n"
        else:
            markdown_content += "### 不同渠道生图次数&生图用户数\n"
            # 加载图
            if "aiping_gen_image_trend.png" in cdn_urls:
                markdown_content += f"![爱拼长图生图数据]({cdn_urls['aiping_gen_image_trend.png']})\n\n"
            # 加载表格  
            markdown_content += report_data["aiping_gen_image_data"].to_markdown(index=False) + "\n\n"

      
        # 经营指标 - 赫鲤显微镜
        markdown_content += "## 赫鲤显微镜 - 近七天经营指标\n"
        
        # 按天展示收入数据
        if "heli_income_by_day" not in report_data or report_data["heli_income_by_day"].empty:
            markdown_content += "> 暂无收入数据。\n"
        else:
            markdown_content += "### 收入数据（按天展示）\n"
            # 加载图
            if "heli_income_trend.png" in cdn_urls:
                markdown_content += f"![赫鲤收入趋势图]({cdn_urls['heli_income_trend.png']})\n\n"
            # 加载表格  
            markdown_content += report_data["heli_income_by_day"].to_markdown(index=False) + "\n\n"


        # 昨天转化数据
        if "conversion_funnel.png" in cdn_urls:
            markdown_content += f"![赫鲤转化漏斗图]({cdn_urls['conversion_funnel.png']})\n\n"
            
        # 添加转化数据表格
        if "conversion_data" in report_data and not report_data["conversion_data"].empty:
            markdown_content += "#### 转化漏斗明细数据\n"
            # 计算转化率
            df_conversion = report_data["conversion_data"].copy()
            df_conversion['隐私页转化率'] = (df_conversion['隐私页用户'] / df_conversion['启动用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            df_conversion['荷包页转化率'] = (df_conversion['荷包页用户'] / df_conversion['隐私页用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            df_conversion['登录页转化率'] = (df_conversion['登录页用户'] / df_conversion['荷包页用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            df_conversion['登录成功转化率'] = (df_conversion['登录成功用户'] / df_conversion['登录页用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            df_conversion['付费页转化率'] = (df_conversion['付费页用户'] / df_conversion['登录成功用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            df_conversion['确认付费转化率'] = (df_conversion['确认付费用户'] / df_conversion['付费页用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            df_conversion['付费成功转化率'] = (df_conversion['付费成功用户'] / df_conversion['确认付费用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            markdown_content += df_conversion.to_markdown(index=False) + "\n\n"

        markdown_content += "### 不同渠道指标\n"
        if "heli_channel_metrics" not in report_data or report_data["heli_channel_metrics"].empty:
            markdown_content += "> 暂无渠道指标数据。\n"
        else:
            # 计算登录率和付费率
            df_channel = report_data["heli_channel_metrics"].copy()
            df_channel['登录率'] = (df_channel['登录成功用户'] / df_channel['启动用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            df_channel['会员付费率'] = (df_channel['付费用户'] / df_channel['启动用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            # 加载图
            if "heli_channel_metrics_combined.png" in cdn_urls: 
                markdown_content += f"![赫鲤渠道指标]({cdn_urls['heli_channel_metrics_combined.png']})\n\n"
            if "heli_channel_combined.png" in cdn_urls: 
                markdown_content += f"![赫鲤渠道指标]({cdn_urls['heli_channel_combined.png']})\n\n"
            # 加载表格  
            markdown_content += df_channel.to_markdown(index=False) + "\n\n"
                
        markdown_content += "### 赫宝策略配置\n"
        if "heli_hebao_metrics" not in report_data or report_data["heli_hebao_metrics"].empty:
            markdown_content += "> 暂无赫宝数据。\n"
        else:
            markdown_content += report_data["heli_hebao_metrics"].to_markdown(index=False) + "\n\n"
            
        # 赫鲤策略配置总结
        if "heli_hebao_policy" not in report_data or report_data["heli_hebao_policy"].empty:
            markdown_content += "> 暂无会员策略配置数据。\n"
        else:
            markdown_content += report_data["heli_hebao_policy"].to_markdown(index=False) + "\n\n"


        markdown_content += "\n"

        print("Markdown report generated successfully.")
        return markdown_content

    def generate_report(self, data_date: datetime) -> str:
        """
        根据从JSON文件读取的数据生成Markdown格式的总结报告。
        """
        print("Generating Markdown report...")
        
        # 加载所有数据
        raw_data = self._load_all_data()
        
        # 处理数据
        report_data = self._process_data(raw_data)
        
        cdn_urls = self._upload_to_oss()
        markdown_content = f"# 运营数据日报 - {data_date.strftime('%Y年%m月%d日')}\n\n"

        # 市场情况总结
        markdown_content += "## 市场情况\n"
        markdown_content += "> 暂无应用上下架情况数据。\n"
        markdown_content += "\n"


        # 经营指标 - 爱拼长图
        markdown_content += "## 爱拼长图 - 近七天经营指标\n"
        if "aiping_dau" not in report_data or report_data["aiping_dau"].empty:
            markdown_content += "> 暂无用户活跃数据。\n"
        else:
            markdown_content += "### 用户活跃数据\n"
            # 加载图
            if "aiping_dau_trend.png" in cdn_urls:
                markdown_content += f"![爱拼长图DAU趋势图]({cdn_urls['aiping_dau_trend.png']})\n\n"



        if "aiping_gen_image_data" not in report_data or report_data["aiping_gen_image_data"].empty:
            markdown_content += "> 暂无生图次数&生图用户数数据。\n"
        else:
            markdown_content += "### 不同渠道生图次数&生图用户数\n"
            # 加载图
            if "aiping_gen_image_trend.png" in cdn_urls:
                markdown_content += f"![爱拼长图生图数据]({cdn_urls['aiping_gen_image_trend.png']})\n\n"
      
        # 经营指标 - 赫鲤显微镜
        markdown_content += "## 赫鲤显微镜 - 近七天经营指标\n"
        
        # 按天展示收入数据
        if "heli_income_by_day" not in report_data or report_data["heli_income_by_day"].empty:
            markdown_content += "> 暂无收入数据。\n"
        else:
            markdown_content += "### 收入数据（按天展示）\n"
            # 加载图
            if "heli_income_trend.png" in cdn_urls:
                markdown_content += f"![赫鲤收入趋势图]({cdn_urls['heli_income_trend.png']})\n\n"

        # 添加转化数据表格
        if "conversion_data" in report_data and not report_data["conversion_data"].empty:
            markdown_content += "### 昨日转化数据\n"     
            if "conversion_funnel.png" in cdn_urls:
                markdown_content += f"![赫鲤转化漏斗图]({cdn_urls['conversion_funnel.png']})\n\n"
        else:
            markdown_content += "> 暂无转化漏斗数据。\n"
            
        # # 添加转化数据表格
        # if "conversion_data" in report_data and not report_data["conversion_data"].empty:
        #     markdown_content += "#### 转化漏斗明细数据\n"
        #     # 计算转化率
        #     df_conversion = report_data["conversion_data"].copy()
        #     df_conversion['隐私页转化率'] = (df_conversion['隐私页用户'] / df_conversion['启动用户']).fillna(0).apply(lambda x: f"{x:.2%}")
        #     df_conversion['荷包页转化率'] = (df_conversion['荷包页用户'] / df_conversion['隐私页用户']).fillna(0).apply(lambda x: f"{x:.2%}")
        #     df_conversion['登录页转化率'] = (df_conversion['登录页用户'] / df_conversion['荷包页用户']).fillna(0).apply(lambda x: f"{x:.2%}")
        #     df_conversion['登录成功转化率'] = (df_conversion['登录成功用户'] / df_conversion['登录页用户']).fillna(0).apply(lambda x: f"{x:.2%}")
        #     df_conversion['付费页转化率'] = (df_conversion['付费页用户'] / df_conversion['登录成功用户']).fillna(0).apply(lambda x: f"{x:.2%}")
        #     df_conversion['确认付费转化率'] = (df_conversion['确认付费用户'] / df_conversion['付费页用户']).fillna(0).apply(lambda x: f"{x:.2%}")
        #     df_conversion['付费成功转化率'] = (df_conversion['付费成功用户'] / df_conversion['确认付费用户']).fillna(0).apply(lambda x: f"{x:.2%}")
        #     markdown_content += df_conversion.to_markdown(index=False) + "\n\n"

        markdown_content += "### 不同渠道指标\n"
        if "heli_channel_metrics" not in report_data or report_data["heli_channel_metrics"].empty:
            markdown_content += "> 暂无渠道指标数据。\n"
        else:
            # 计算登录率和付费率
            df_channel = report_data["heli_channel_metrics"].copy()
            df_channel['登录率'] = (df_channel['登录成功用户'] / df_channel['启动用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            df_channel['会员付费率'] = (df_channel['付费用户'] / df_channel['启动用户']).fillna(0).apply(lambda x: f"{x:.2%}")
            # 加载图
            if "heli_channel_metrics_combined.png" in cdn_urls: 
                markdown_content += f"![赫鲤渠道指标]({cdn_urls['heli_channel_metrics_combined.png']})\n\n"
            if "heli_channel_combined.png" in cdn_urls: 
                markdown_content += f"![赫鲤渠道指标]({cdn_urls['heli_channel_combined.png']})\n\n"
                
        markdown_content += "### 赫宝策略配置\n"
        if "heli_hebao_metrics" not in report_data or report_data["heli_hebao_metrics"].empty:
            markdown_content += "> 暂无赫宝数据。\n"
        else:
            markdown_content += report_data["heli_hebao_metrics"].to_markdown(index=False) + "\n\n"
            
        # 赫鲤策略配置总结
        if "heli_hebao_policy" not in report_data or report_data["heli_hebao_policy"].empty:
            markdown_content += "> 暂无会员策略配置数据。\n"
        else:
            markdown_content += report_data["heli_hebao_policy"].to_markdown(index=False) + "\n\n"

        markdown_content += "\n"

        print("Markdown report generated successfully.")
        return markdown_content
