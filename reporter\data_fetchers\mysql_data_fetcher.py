# data_fetchers/sql_data_fetcher.py
import pandas as pd
from sqlalchemy import create_engine

class SQLDataFetcher:
    def __init__(self, db_url: str):
        self.engine = create_engine(db_url)

    def _execute_query(self, query: str) -> pd.DataFrame:
        """
        执行SQL查询并返回DataFrame。
        """
        try:
            with self.engine.connect() as connection:
                print(query)
                df = pd.read_sql(query, connection)
            return df
        except Exception as e:
            print(f"Error executing SQL query: {e}")
            return pd.DataFrame()
        
    def get_heli_income_sum(self, start_date: str, end_date: str) -> dict:

        query = f"""
            SELECT DATE_FORMAT(create_time,  "%%Y-%%m-%%d") as data_date, SUM(price) as income
            FROM guniverse_order 
            WHERE create_time >= "{start_date} 00:00:00" AND create_time <= "{end_date} 23:59:59" and status = "1" 
            group by DATE_FORMAT(create_time, "%%Y-%%m-%%d")
        """
        return self._execute_query(query).to_dict(orient='records')
    
    def get_heli_hebao_active(self) -> dict:

        query = f"""
            SELECT id as app_type, name, landing_type, mat_type 
            FROM guniverse_hebao 
            WHERE status = "1" 
        """
        return self._execute_query(query).to_dict(orient='records')

    def get_heli_hebao_policy(self) -> dict:

        query = f"""
            SELECT id, name, orig_price, cur_price   
            FROM guniverse_group  
            WHERE source_type = "hebao" and status = "1" 
        """
        return self._execute_query(query).to_dict(orient='records')

    def get_star_user_phone(self)  -> dict:
        
        query = f"""
            SELECT id, phone from star_user
        """

        return self._execute_query(query).to_dict(orient='records')
    
    