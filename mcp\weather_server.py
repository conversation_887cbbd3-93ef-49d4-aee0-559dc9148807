#!/usr/bin/env python
# -*- coding:utf-8 -*-

from typing import List
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("Weather", host="0.0.0.0", port=8001)

@mcp.tool()
def add(a: int, b: int) -> int:
    """Add two numbers"""
    return a + b

@mcp.tool()
def multiply(a: int, b: int) -> int:
    """Multiply two numbers"""
    return a * b

@mcp.tool()
async def get_weather(location: str) -> str:
    """Get weather for location."""
    return "It's always sunny in New York"

if __name__ == "__main__":
    mcp.run(transport="sse")
    
    
    
    
