import requests
import base64
import os
import json

import sys

print(os.environ.get('HTTP_PROXY'))
print(os.environ.get('HTTPS_PROXY'))
print(os.environ.get('NO_PROXY'))

file_path = sys.argv[1]
api_endpoint = 'http://127.0.0.1:8766/api/embedding'

try:
    with open(file_path, 'rb') as f:
        image_data = f.read()
    content = str(base64.b64encode(image_data), encoding="utf-8")
    data = {'image': content, "userId":"test", "dsName":file_path}
    response = requests.post(api_endpoint, data=json.dumps(data))

    print(f"Status Code: {response.status_code}")
    print(f"Response Body: {response.text}")

except FileNotFoundError:
    print(f"Error: The file {file_path} was not found.")
except Exception as e:
    print(f"An error occurred: {e}")
