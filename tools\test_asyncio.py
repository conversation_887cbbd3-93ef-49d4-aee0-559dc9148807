
import asyncio
import sys

# This block is crucial for Windows users
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

loop = asyncio.get_running_loop()
print(type(loop))

async def main():
    print("Starting application...")
    loop = asyncio.get_running_loop()
    print(type(loop))
    print("Application finished.")

if __name__ == "__main__":
    asyncio.run(main())
