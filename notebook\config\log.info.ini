[loggers]
keys=root,req,chat

[handlers]
keys=logconsole,logfile,reqfile,chatfile

[formatters]
keys=logformatter,reqformatter

[logger_root]
level=INFO
handlers=logconsole,logfile
qualname=root
propagate=0

[logger_req]
level=INFO
handlers=logconsole,reqfile
qualname=req
propagate=0

[logger_chat]
level=INFO
handlers=chatfile
qualname=chat
propagate=1

[formatter_logformatter]
format=[%(asctime)s.%(msecs)03d] %(levelname)s [%(thread)d] - %(message)s

[formatter_reqformatter]
format=%(message)s

[handler_logconsole]
class=handlers.logging.StreamHandler
level=INFO
args=()
formatter=logformatter

[handler_logfile]
class=handlers.ConcurrentTimedRotatingFileHandler
kwargs={'filename':'./logs/api-data.log', 'when':'M', 'interval':20, 'maxBytes':0, 'backupCount':1,'encoding':'utf-8'}
level=INFO
formatter=logformatter

[handler_reqfile]
class=handlers.ConcurrentTimedRotatingFileHandler
kwargs={'filename':'./logs/req-data.log', 'when':'M', 'interval':20, 'maxBytes':0, 'backupCount':1,'encoding':'utf-8'}
level=INFO
formatter=reqformatter

[handler_chatfile]
class=handlers.ConcurrentTimedRotatingFileHandler
kwargs={'filename':'./logs/chat-data.log', 'when':'M', 'interval':20, 'maxBytes':0, 'backupCount':1,'encoding':'utf-8'}
level=INFO
formatter=reqformatter







