#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from fastapi import APIRouter

from .dev import router as dev_router
from .docs import custom_docs
from .user import router as user_router
from .embedding import router as embedding_router
from .datasource import router as datasource_router 
from .llm import router as llm_router
from ..config import get_settings
settings = get_settings()

api_router = APIRouter(prefix=settings.url_prefix)

if settings.debug:
    api_router.include_router(dev_router)

api_router.include_router(user_router)
api_router.include_router(embedding_router)
api_router.include_router(datasource_router)
api_router.include_router(llm_router)

