#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import typing
from .resp import RespCode
import logging
root_logger = logging.getLogger("root")

class APIException(Exception):
    def __init__(
        self,
        detail: typing.Optional[str] = None
    ) -> None:
       
        self.detail = detail
       
    def __repr__(self) -> str:
        class_name = self.__class__.__name__
        root_logger.info(f"{class_name}(detail={self.detail!r})")
        return RespCode.resp_err()
    
class UserIdIsNullException(APIException):
    def __init__(self) -> None:
        super().__init__("用户id为空")

class PasswordMustSameException(APIException):
    def __init__(self) -> None:
        super().__init__("两个密码不一致")

