"""
Copyright 2025, Zep Software, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from graphiti_core import Graphiti
from graphiti_core.utils.maintenance.graph_data_operations import clear_data
# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)

logger = logging.getLogger(__name__)

profile = os.environ.get("PROFILE", "DEV")
if profile == "DEV":
    load_dotenv(".env.development", override=True)
if profile == "TEST":
    load_dotenv(".env.test", override=True)
if profile == "PROD":
    load_dotenv(".env.production", override=True)

# Neo4j connection parameters
# Make sure Neo4j Desktop is running with a local DBMS started
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://192.168.3.31:7687')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'neo4j_data')

if not neo4j_uri or not neo4j_user or not neo4j_password:
    raise ValueError('NEO4J_URI, NEO4J_USER, and NEO4J_PASSWORD must be set')

async def main():

    graphiti = Graphiti(neo4j_uri, neo4j_user, neo4j_password)
    try:
        # await clear_data(graphiti.driver)
        # await graphiti.build_indices_and_constraints()
        
        await graphiti.build_communities()
    finally:
        # Close the connection
        await graphiti.close()

if __name__ == '__main__':
    asyncio.run(main())
