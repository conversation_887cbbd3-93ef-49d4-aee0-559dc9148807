
from graphiti_core import <PERSON>rap<PERSON>i
from graphiti_core.edges import EntityEdge
from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS
from graphiti_core.nodes import EntityNode, EpisodicNode
from graphiti_core.llm_client import LL<PERSON>lient
from graphiti_core.embedder import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.errors import EdgeNotFoundError, GroupsEdgesNotFoundError, NodeNotFoundError
from pydantic_settings import BaseSettings, SettingsConfigDict
from contextlib import asynccontextmanager
from pydantic import Field

import uuid
import asyncio
import logging
import traceback

logger = logging.getLogger(__name__)

class GraphitiSettings(BaseSettings):

    openai_api_key: str
    openai_base_url: str | None = Field(None)
    model_name: str | None = Field(None)
    embedding_model_name: str | None = Field(None)
    embedding_dim: int | None = Field(None)

    neo4j_uri: str
    neo4j_user: str
    neo4j_password: str
    model_config = SettingsConfigDict(extra='ignore')

class AsyncWorker:
    def __init__(self):
        self.queue = asyncio.Queue()
        self.task = None

    async def worker(self):
        while True:
            try:
                uniq_id = str(uuid.uuid4())
                logger.info(f'Got a job: {uniq_id}, (size of remaining queue: {self.queue.qsize()})')
                job = await self.queue.get()
                await job()
                logger.info(f'Job done: {uniq_id}, (size of remaining queue: {self.queue.qsize()})')
            except Exception as e:
                logger.error(f'Job {uniq_id} Execution Encountered an error in worker: {traceback.format_exc()}')
            except asyncio.CancelledError:
                break

    async def start(self):
        self.task = asyncio.create_task(self.worker())

    async def stop(self):
        if self.task:
            self.task.cancel()
            await self.task
        while not self.queue.empty():
            self.queue.get_nowait()

class ZepGraphiti(Graphiti):
    def __init__(self, uri: str, user: str, password: str, llm_client: LLMClient | None = None, embedder: OpenAIEmbedder | None = None):
        super().__init__(uri, user, password, llm_client, embedder)

    async def save_entity_node(self, name: str, uuid:str, group_id: str, summary: str = ''):
        
        new_node = EntityNode(
            name=name,
            uuid=uuid,
            group_id=group_id,
            summary=summary,
        )
        await new_node.generate_name_embedding(self.embedder)
        await new_node.save(self.driver)
        return new_node

    async def get_entity_edge(self, uuid: str):
        try:
            edge = await EntityEdge.get_by_uuid(self.driver, uuid)
            return edge
        except EdgeNotFoundError as e:
            return None

    async def delete_group(self, group_id: str):
        try:
            edges = await EntityEdge.get_by_group_ids(self.driver, [group_id])
        except GroupsEdgesNotFoundError:
            logger.warning(f'No edges found for group {group_id}')
            edges = []

        nodes = await EntityNode.get_by_group_ids(self.driver, [group_id])

        episodes = await EpisodicNode.get_by_group_ids(self.driver, [group_id])

        for edge in edges:
            await edge.delete(self.driver)

        for node in nodes:
            await node.delete(self.driver)

        for episode in episodes:
            await episode.delete(self.driver)

    async def delete_entity_edge(self, uuid: str):
        try:
            edge = await EntityEdge.get_by_uuid(self.driver, uuid)
            await edge.delete(self.driver)
        except EdgeNotFoundError as e:
            pass

    async def delete_episodic_node(self, uuid: str):
        try:
            episode = await EpisodicNode.get_by_uuid(self.driver, uuid)
            await episode.delete(self.driver)
        except NodeNotFoundError as e:
            pass

@asynccontextmanager
async def get_graphiti(settings: GraphitiSettings):

    embedder = OpenAIEmbedder(OpenAIEmbedderConfig(
        api_key=settings.openai_api_key,
        base_url=settings.openai_base_url,
        embedding_model=settings.embedding_model_name
    ))

    client = ZepGraphiti(
        uri=settings.neo4j_uri,
        user=settings.neo4j_user,
        password=settings.neo4j_password,
        embedder=embedder
    )

    if settings.openai_base_url is not None:
        client.llm_client.config.base_url = settings.openai_base_url
    if settings.openai_api_key is not None:
        client.llm_client.config.api_key = settings.openai_api_key
    if settings.model_name is not None:
        client.llm_client.model = settings.model_name

    try:
        yield client
    finally:
        await client.close()

