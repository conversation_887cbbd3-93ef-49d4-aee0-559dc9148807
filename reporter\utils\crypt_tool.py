import struct
import base64
import hashlib
import logging
from Crypto.Cipher import AES

logger = logging.getLogger(__name__)

def decrypt_banyun_java(cipher_b64: str) -> str:
    """
    解密Java javax.crypto.Cipher加密的数据，包含header信息
    
    Args:
        cipher_b64: Base64编码的密文（包含4字节header + 实际密文）
        
    Returns:
        解密后的明文字符串
    """
    try:
        # Base64 decode
        cipher_bytes = base64.b64decode(cipher_b64)
        logger.debug(f"Total cipher bytes length: {len(cipher_bytes)}")
        
        if len(cipher_bytes) < 4:
            raise ValueError("Cipher data too short - missing header")
        
        # 解析4字节header
        header = cipher_bytes[:4]
        actual_cipher = cipher_bytes[4:]
        
        # 解析header (使用小端字节序，与Java版本一致)
        magic_number = struct.unpack('<H', header[:2])[0]  # little-endian short
        logger.debug(f"Magic number: 0x{magic_number:04X}")
        
        if magic_number != 0xA8B2:
            raise ValueError(f"Invalid magic number: 0x{magic_number:04X}, expected 0xA8B2")
        
        # 第3字节：低4位是version，高4位是type index
        b3 = header[2]
        version = b3 & 0x0F
        type_index = (b3 >> 4) & 0x0F
        
        logger.debug(f"Version: {version}, Type index: {type_index}")
        
        if version != 1:
            raise ValueError(f"Invalid cipher version: {version}, expected 1")
        
        if type_index != 1:  # Kratos type
            raise ValueError(f"Unsupported cipher type: {type_index}, expected 1 (Kratos)")
        
        # 第4字节：低2位是key index，第3位是simple chunk标志
        b4 = header[3]
        key_index = b4 & 0x03
        simple_chunk = (b4 >> 2) & 0x01
        
        logger.debug(f"Key index: {key_index}, Simple chunk: {simple_chunk}")
        
        if key_index != 0:
            raise ValueError(f"Unsupported key index: {key_index}, expected 0")
        
        # 使用配置信息解密
        raw_key = "@%jupiter%!"
        
        # MD5生成16字节key
        key = hashlib.md5(raw_key.encode("utf-8")).digest()
        logger.debug(f"Generated key length: {len(key)}")
        
        # IV生成：simple chunk情况下全0，否则应该从数据中提取
        if simple_chunk == 1:
            iv = bytes([0] * 16)
            cipher_data = actual_cipher
        else:
            # Chunked模式：前16字节是IV
            if len(actual_cipher) < 16:
                raise ValueError("Cipher data too short for chunked mode - missing IV")
            iv = actual_cipher[:16]
            cipher_data = actual_cipher[16:]
        
        logger.debug(f"IV length: {len(iv)}, Cipher data length: {len(cipher_data)}")
        
        # 检查密文长度
        if len(cipher_data) % 16 != 0:
            logger.error(f"Cipher data length {len(cipher_data)} is not a multiple of 16")
            raise ValueError("Invalid cipher data length - must be multiple of 16 bytes")
        
        # AES/CBC/PKCS5Padding 解密
        cipher = AES.new(key, AES.MODE_CBC, iv)
        plaintext = cipher.decrypt(cipher_data)
        
        # 去掉 PKCS5 padding
        if len(plaintext) == 0:
            logger.error("Decrypted plaintext is empty")
            raise ValueError("Decryption resulted in empty data")
        
        pad_len = plaintext[-1]
        
        # 验证padding的有效性
        if pad_len > 16 or pad_len == 0:
            logger.error(f"Invalid padding length: {pad_len}")
            raise ValueError(f"Invalid PKCS5 padding length: {pad_len}")
        
        # 检查padding字节是否正确
        for i in range(pad_len):
            if plaintext[-(i+1)] != pad_len:
                logger.error("Invalid PKCS5 padding bytes")
                raise ValueError("Invalid PKCS5 padding")
        
        plaintext = plaintext[:-pad_len]
        result = plaintext.decode("utf-8")
        logger.info("Successfully decrypted Java cipher data")
        return result
        
    except base64.binascii.Error as e:
        logger.error(f"Base64 decode error: {str(e)}")
        raise ValueError(f"Invalid base64 input: {str(e)}")
    except UnicodeDecodeError as e:
        logger.error(f"UTF-8 decode error: {str(e)}")
        raise ValueError(f"Decrypted data is not valid UTF-8: {str(e)}")
    except struct.error as e:
        logger.error(f"Header parsing error: {str(e)}")
        raise ValueError(f"Invalid header format: {str(e)}")
    except Exception as e:
        logger.error(f"Decryption failed: {str(e)}")
        raise

def encrypt_banyun_java(plaintext: str, simple_chunk: bool = True) -> str:
    """
    使用Java兼容的格式加密数据（包含header）
    
    Args:
        plaintext: 要加密的明文字符串
        simple_chunk: 是否使用simple chunk模式（全0 IV）
        
    Returns:
        Base64编码的密文（包含header）
    """
    try:
        raw_key = "@%jupiter%!"
        key = hashlib.md5(raw_key.encode("utf-8")).digest()
        
        # 构建header
        magic_number = 0xA8B2
        version = 1
        type_index = 1  # Kratos
        key_index = 0
        
        # 构建header字节 (使用小端字节序，与Java版本一致)
        header = bytearray(4)
        header[0:2] = struct.pack('<H', magic_number)  # little-endian short
        header[2] = (type_index << 4) | version
        header[3] = (1 if simple_chunk else 0) << 2 | key_index
        
        # 准备加密数据
        plaintext_bytes = plaintext.encode("utf-8")
        
        # PKCS5 padding
        pad_len = 16 - (len(plaintext_bytes) % 16)
        padded_plaintext = plaintext_bytes + bytes([pad_len] * pad_len)
        
        # 生成IV
        if simple_chunk:
            iv = bytes([0] * 16)
            cipher_data = padded_plaintext
        else:
            import os
            iv = os.urandom(16)
            cipher_data = padded_plaintext
        
        # AES/CBC 加密
        cipher = AES.new(key, AES.MODE_CBC, iv)
        encrypted_data = cipher.encrypt(cipher_data)
        
        # 组装最终数据：header + [IV] + encrypted_data
        if simple_chunk:
            final_data = header + encrypted_data
        else:
            final_data = header + iv + encrypted_data
        
        # Base64 encode
        cipher_b64 = base64.b64encode(final_data).decode("utf-8")
        logger.info("Successfully encrypted data with Java-compatible format")
        return cipher_b64
        
    except Exception as e:
        logger.error(f"Encryption failed: {str(e)}")
        raise

if __name__ == "__main__":
    # 测试Java格式的解密
    test_java_data = "sqgRBASNSXY/wVqO8N+nLr53FQI="  # 替换为实际的Java加密数据
    
    try:
        result = decrypt_banyun_java(test_java_data)
        print(f"Java decryption result: {result}")
    except Exception as e:
        print(f"Java decryption test failed: {str(e)}")
    
    # 测试加密解密循环
    try:
        original_text = "Hello, Java World!"
        encrypted = encrypt_banyun_java(original_text)
        decrypted = decrypt_banyun_java(encrypted)
        print(f"Java round-trip test: '{original_text}' -> {encrypted} -> '{decrypted}' (Success: {original_text == decrypted})")
    except Exception as e:
        print(f"Java round-trip test failed: {str(e)}")
