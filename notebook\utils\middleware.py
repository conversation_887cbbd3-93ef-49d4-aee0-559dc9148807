#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from fastapi import FastAP<PERSON>,Request
import time
from ..auth import decodeJWT
from .resp import RespCode
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from ..schemas.user import UserRequestContent

from fastapi.middleware.gzip import GZipMiddleware

import logging
req_logger = logging.getLogger("req")

def custom_middleware(app: FastAPI):
    @app.middleware("http")
    async def add_process_time_header(request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time

        # X- 作为前缀代表专有自定义请求头
        response.headers["X-Process-Time"] = str(process_time)
        return response

    @app.middleware("http")
    async def validate_token(request: Request, call_next):
        
        url_path = request.url.path
        response = None

        if url_path.find("doc") >= 0 or \
            url_path.find("swagger") >= 0 or \
            url_path.find("dev") >= 0 or \
            url_path.find("openapi") >= 0  or \
            url_path.find("favicon") >= 0:
                
            stime = time.time()
            user = UserRequestContent(**{"userId":"admin", "message":"hello"})
            response = await call_next(request)
            ctime = time.time() - stime
        else:
            stime = time.time()
            payload = request.body
            if payload:
                response = await call_next(request)
                if(request.path_params == "api/llm"):
                    user = UserRequestContent.model_validate(payload)
                else:
                    user = user = UserRequestContent(**{"userId":"admin", "message":"hello"})
            else:
                user = UserRequestContent(**{"userId":"admin", "message":"hello"})
                response = JSONResponse(content=jsonable_encoder(RespCode.resp_err("无效的参数.")))
            ctime = time.time() - stime

        req_logger.info(f"{int(stime)},{request.client.host},{request.url.path},{ctime:.02f},{response.status_code},{user.userId},{user.message}")
            
        return response
    
    app.add_middleware(GZipMiddleware, minimum_size=1024)
