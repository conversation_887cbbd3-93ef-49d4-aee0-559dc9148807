#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from fastapi import APIRouter, Depends, FastAPI
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from starlette.requests import Request
from starlette.responses import HTMLResponse

from ..config import get_settings
from ..libs.db_lib import db
from ..utils import hash_tool

settings = get_settings()

router = APIRouter()

def custom_docs(application: FastAPI):
    async def custom_swagger_ui_html():
        
        return get_swagger_ui_html(
                openapi_url=application.openapi_url,
                title=application.title + " - Swagger UI",
                swagger_js_url="/static/swagger/swagger-ui-bundle.js",
                swagger_css_url="/static/swagger/swagger-ui.css",
                swagger_ui_parameters= application.swagger_ui_parameters,
                )

    async def redoc_html():
        return get_redoc_html(
                openapi_url=application.openapi_url,
                title=application.title + " - ReDoc",
                redoc_js_url="/static/redoc/redoc.standalone.js")


    if settings.debug:
        application.get("/docs", include_in_schema=False)(custom_swagger_ui_html)
        application.get("/redoc", include_in_schema=False)(redoc_html)

