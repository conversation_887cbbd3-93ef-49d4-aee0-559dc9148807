import asyncio
import logging
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>
from ..config import get_settings # Assuming this path is correct relative to your project structure
from psycopg_pool import AsyncConnectionPool

# Configure logging
logger = logging.getLogger("root")
# --- Configuration ---
KEEP_ALIVE_INTERVAL_SECONDS = 5 * 60  # 5 minutes

# --- Periodic Task ---
async def periodic_db_keep_alive(pool: AsyncConnectionPool):
    """
    Periodically executes a simple query on the database pool
    to keep connections alive and check pool health.
    """
    while True:
        try:
            # Wait for the interval first, except for the very first run
            # Use a small initial delay if needed, or run immediately first time
            # await asyncio.sleep(1) # Optional small initial delay

            logger.info(f"Running periodic DB keep-alive check (interval: {KEEP_ALIVE_INTERVAL_SECONDS}s)...")
            async with pool.connection() as conn:
                async with conn.cursor() as cur:
                    # Execute a lightweight query
                    await cur.execute("SELECT 1;")
                    result = await cur.fetchone()
                    if result and result[0] == 1:
                        logger.debug("DB keep-alive check successful (SELECT 1 executed).")
                    else:
                        logger.warning("DB keep-alive check query did not return expected result.")
            
            # Wait for the defined interval
            await asyncio.sleep(KEEP_ALIVE_INTERVAL_SECONDS)

        except asyncio.CancelledError:
            # This is expected when the application shuts down
            logger.info("DB keep-alive task cancelled.")
            break # Exit the loop cleanly
        except Exception as e:
            logger.error(f"Error during periodic DB keep-alive check: {e}", exc_info=True)
            # Avoid busy-looping in case of persistent errors, wait before retrying
            logger.info(f"Waiting {KEEP_ALIVE_INTERVAL_SECONDS}s after error before next keep-alive check...")
            try:
                await asyncio.sleep(KEEP_ALIVE_INTERVAL_SECONDS)
            except asyncio.CancelledError:
                logger.info("DB keep-alive task cancelled during error sleep.")
                break # Exit if cancelled while waiting after an error

# --- Lifespan Manager ---
@asynccontextmanager
async def lifespan_db(app: FastAPI):
    """
    Manages the FastAPI application lifespan for database connections.
    Initializes the connection pool and starts a periodic keep-alive task.
    Ensures clean shutdown of the task and the pool.
    """
    settings = get_settings()
    connection_kwargs = {
        "autocommit": True,
        "prepare_threshold": 0, # Consider tuning this based on usage
    }
    pool = None
    keep_alive_task = None

    try:
        logger.info("Initializing PostgreSQL connection pool...")
        pool = AsyncConnectionPool(
            conninfo=settings.postgres_db_uri,
            max_size=5, # Adjust pool size as needed
            kwargs=connection_kwargs,
            # Consider adding min_size, timeouts, etc.
            # min_size=1,
            # open_timeout=10, # Timeout for getting a connection
            # check=AsyncConnectionPool.check_connection, # Optional connection check on acquire
        )
        # Wait for the pool to establish minimum connections if min_size > 0
        # await pool.wait()
        app.state.postgres_pool = pool
        logger.info("PostgreSQL connection pool initialized.")

        # Start the periodic keep-alive task in the background
        logger.info(f"Starting periodic DB keep-alive task (interval: {KEEP_ALIVE_INTERVAL_SECONDS}s)...")
        keep_alive_task = asyncio.create_task(periodic_db_keep_alive(pool))

        yield

    finally:
        logger.info("Shutting down application...")

        # 1. Cancel the keep-alive task
        if keep_alive_task and not keep_alive_task.done():
            logger.info("Cancelling DB keep-alive task...")
            keep_alive_task.cancel()
            try:
                # Wait for the task to actually finish cancelling
                await keep_alive_task
            except asyncio.CancelledError:
                logger.info("DB keep-alive task successfully cancelled.")
            except Exception as e:
                # Log errors during task cancellation/awaiting, but continue shutdown
                logger.error(f"Error awaiting cancelled keep-alive task: {e}", exc_info=True)

        # 2. Close the connection pool
        if pool: # Check if pool was successfully created
            logger.info("Closing PostgreSQL connection pool...")
            await pool.close()
            logger.info("PostgreSQL connection pool closed.")
        
        # Optional: Clear the state
        if hasattr(app.state, 'postgres_pool'):
             app.state.postgres_pool = None

        if hasattr(app.state, "mcp_client"):
            app.state.mcp_client = None

        logger.info("Application shutdown complete.")
        