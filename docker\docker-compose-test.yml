networks:
    compose:
        driver: bridge
        ipam:
            driver: default
            config:
            - subnet: **********/24
              gateway: **********
            
volumes:
    langgraph-data:
        driver: local
    chromadb_data:
        driver: local
    neo4j_data:
        driver: local

services:
    langgraph-postgres:
        image: pgvector/pgvector:pg16
        ports:
            - "5432:5432"
        environment:
            POSTGRES_DB: postgres
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: postgres
        volumes:
            - langgraph-data:/var/lib/postgresql/data
        healthcheck:
            test: pg_isready -U postgres
            start_period: 10s
            timeout: 1s
            retries: 5
            interval: 5s
        networks:
            compose:
                ipv4_address: **********
    langraph-chromadb:
        image: chromadb/chroma:0.6.2  
        container_name: chromadb
        ports:
            - "8000:8000"  # 映射 ChromaDB API 端口
        volumes:
            - chromadb_data:/chroma/chroma  # 持久化存储
            - ../docker/server.htpasswd:/chroma/chroma/server.htpasswd
        environment:
            - IS_PERSISTENT=TRUE  # 使 ChromaDB 数据持久化
            - CHROMA_SERVER_AUTHN_CREDENTIALS_FILE=/chroma/chroma/server.htpasswd
            - CHROMA_SERVER_AUTHN_PROVIDER=chromadb.auth.basic_authn.BasicAuthenticationServerProvider
        networks:
            compose:
                ipv4_address: **********
    bluestar-api:
        image: bluestar:latest
        container_name: bluestar-api
        ports:
            - "8766:8766"
        env_file:
            - ../.env.test
        environment:
            - PROFILE="TEST"
        networks:
            compose:
                ipv4_address: **********

    neo4j:
        image: neo4j:5.26.2
        container_name: neo4j
        healthcheck:
        test:
            [
            "CMD-SHELL",
            "wget -qO- http://localhost:${NEO4J_PORT:-7474} || exit 1",
            ]
        interval: 1s
        timeout: 10s
        retries: 10
        start_period: 3s
        ports:
        - "7474:7474" # HTTP
        - "${NEO4J_PORT:-7687}:${NEO4J_PORT:-7687}" # Bolt
        volumes:
        - neo4j_data:/data
        environment:
        - NEO4J_AUTH=neo4j/neo4j_data
        networks:
        compose:
            ipv4_address: **********
        restart: unless-stopped
        