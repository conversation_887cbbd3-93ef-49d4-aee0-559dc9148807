
from graphiti_core import <PERSON><PERSON><PERSON><PERSON>
from graphiti_core.edges import EntityEdge
from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_EPISODE_MENTIONS
from graphiti_core.nodes import EntityNode, EpisodicNode, EpisodeType
from graphiti_core.search.search_filters import <PERSON><PERSON><PERSON><PERSON>, DateF<PERSON>er, ComparisonOperator
from graphiti_core.llm_client import LL<PERSON>lient,  LLMConfig
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.cross_encoder import CrossEncoderClient, OpenAIRerankerClient
from graphiti_core.embedder import OpenAIEmbedder, OpenAIEmbedderConfig

from graphiti_core.errors import EdgeNotFoundError, GroupsEdgesNotFoundError, NodeNotFoundError

from langgraph.graph import StateGraph, START, END, MessagesState
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore

from typing import Literal, List, Dict, Any
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import BaseModel, <PERSON>
from datetime import datetime, timezone, timedelta
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

from ..config import get_settings
from ..schemas.llm import ContextItem
from .llm_lib import escape_markdown_special_chars, compute_md5
from functools import partial
from contextlib import asynccontextmanager

import uuid
import asyncio
import logging
import time
import traceback

logger = logging.getLogger("chat")
settings = get_settings()

def utc_now() -> datetime:
    """Returns the current UTC datetime with timezone information."""
    return datetime.now(timezone.utc)

class Result(BaseModel):
    message: str
    success: bool

class Message(BaseModel):
    content: str = Field(..., description='The content of the message')
    uuid: str | None = Field(default=None, description='The uuid of the message (optional)')
    name: str = Field(
        default='', description='The name of the episodic node for the message (optional)'
    )
    role_type: Literal['user', 'assistant'] = Field(
        ..., description='The role type of the message (user, assistant)'
    )
    role: str | None = Field(
        description='The custom role of the message to be used alongside role_type (user name, bot name, etc.)',
    )
    timestamp: datetime = Field(default_factory=utc_now, description='The timestamp of the message')
    source_description: str = Field(
        default='', description='The description of the source of the message'
    )

class AddMessagesRequest(BaseModel):
    group_id: str = Field(..., description='The group id of the messages to add')
    messages: list[Message] = Field(..., description='The messages to add')

class AddEntityNodeRequest(BaseModel):
    uuid: str = Field(..., description='The uuid of the node to add')
    group_id: str = Field(..., description='The group id of the node to add')
    name: str = Field(..., description='The name of the node to add')
    summary: str = Field(default='', description='The summary of the node to add')

class SearchQuery(BaseModel):
    group_ids: list[str] | None = Field(
        None, description='The group ids for the memories to search'
    )
    query: str
    max_facts: int = Field(default=10, description='The maximum number of facts to retrieve')

class FactResult(BaseModel):
    uuid: str
    name: str
    fact: str
    valid_at: datetime | None
    invalid_at: datetime | None
    created_at: datetime
    expired_at: datetime | None

    class Config:
        json_encoders = {datetime: lambda v: v.astimezone(timezone.utc).isoformat()}

class SearchResults(BaseModel):
    facts: list[FactResult]

class GetMemoryRequest(BaseModel):
    group_id: str = Field(..., description='The group id of the memory to get')
    max_facts: int = Field(default=10, description='The maximum number of facts to retrieve')
    center_node_uuid: str | None = Field(
        ..., description='The uuid of the node to center the retrieval on'
    )
    messages: list[Message] = Field(
        ..., description='The messages to build the retrieval query from '
    )

class GetMemoryResponse(BaseModel):
    facts: list[FactResult] = Field(..., description='The facts that were retrieved from the graph')

class GraphitiSettings(BaseSettings):

    openai_api_key: str
    openai_base_url: str | None = Field(None)
    model_name: str | None = Field(None)
    embedding_model: str | None = Field(None)
    embedding_dim: int | None = Field(None)

    neo4j_uri: str
    neo4j_user: str
    neo4j_password: str
    model_config = SettingsConfigDict(extra='ignore')

class AsyncWorker:
    def __init__(self):
        self.queue = asyncio.Queue()
        self.task = None

    async def worker(self):
        while True:
            try:
                uniq_id = str(uuid.uuid4())
                logger.info(f'Got a job: {uniq_id}, (size of remaining queue: {self.queue.qsize()})')
                job = await self.queue.get()
                await job()
                logger.info(f'Job done: {uniq_id}, (size of remaining queue: {self.queue.qsize()})')
            except Exception as e:
                logger.error(f'Job {uniq_id} Execution Encountered an error in worker: {traceback.format_exc()}')
            except asyncio.CancelledError:
                break

    async def start(self):
        self.task = asyncio.create_task(self.worker())

    async def stop(self):
        if self.task:
            self.task.cancel()
            await self.task
        while not self.queue.empty():
            self.queue.get_nowait()

class ZepGraphiti(Graphiti):
    def __init__(self, uri: str, user: str, password: str, llm_client: LLMClient | None = None, embedder: OpenAIEmbedder | None = None, cross_encoder: CrossEncoderClient | None = None):
        super().__init__(uri, user, password, llm_client, embedder, cross_encoder)
        
    async def save_entity_node(self, name: str, uuid:str, group_id: str, summary: str = ''):
        
        new_node = EntityNode(
            name=name,
            uuid=uuid,
            group_id=group_id,
            summary=summary,
        )
        await new_node.generate_name_embedding(self.embedder)
        await new_node.save(self.driver)
        return new_node

    async def get_entity_edge(self, uuid: str):
        try:
            edge = await EntityEdge.get_by_uuid(self.driver, uuid)
            return edge
        except EdgeNotFoundError as e:
            return None

    async def delete_group(self, group_id: str):
        try:
            edges = await EntityEdge.get_by_group_ids(self.driver, [group_id])
        except GroupsEdgesNotFoundError:
            logger.warning(f'No edges found for group {group_id}')
            edges = []

        nodes = await EntityNode.get_by_group_ids(self.driver, [group_id])

        episodes = await EpisodicNode.get_by_group_ids(self.driver, [group_id])

        for edge in edges:
            await edge.delete(self.driver)

        for node in nodes:
            await node.delete(self.driver)

        for episode in episodes:
            await episode.delete(self.driver)

    async def delete_entity_edge(self, uuid: str):
        try:
            edge = await EntityEdge.get_by_uuid(self.driver, uuid)
            await edge.delete(self.driver)
        except EdgeNotFoundError as e:
            pass

    async def delete_episodic_node(self, uuid: str):
        try:
            episode = await EpisodicNode.get_by_uuid(self.driver, uuid)
            await episode.delete(self.driver)
        except NodeNotFoundError as e:
            pass

@asynccontextmanager
async def get_graphiti(settings: GraphitiSettings):

    embedder = OpenAIEmbedder(OpenAIEmbedderConfig(
        api_key=settings.openai_api_key,
        base_url=settings.openai_base_url,
        embedding_model=settings.embedding_model,
        embedding_dim=settings.embedding_dim
    ))
    
    llm_client = OpenAIGenericClient(LLMConfig(
        api_key=settings.openai_api_key,
        base_url=settings.openai_base_url,
        model=settings.model_name
    ))
    cross_encoder = OpenAIRerankerClient(LLMConfig(
        api_key=settings.openai_api_key,
        base_url=settings.openai_base_url,
        model=settings.model_name
    ))

    client = ZepGraphiti(
        uri=settings.neo4j_uri,
        user=settings.neo4j_user,
        password=settings.neo4j_password,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder
    )

    if settings.openai_base_url is not None:
        client.llm_client.config.base_url = settings.openai_base_url
    if settings.openai_api_key is not None:
        client.llm_client.config.api_key = settings.openai_api_key
    if settings.model_name is not None:
        client.llm_client.model = settings.model_name

    try:
        yield client
    finally:
        await client.close()

async def initialize_graphiti(settings: GraphitiSettings):
    client = ZepGraphiti(
        uri=settings.neo4j_uri,
        user=settings.neo4j_user,
        password=settings.neo4j_password,
    )
    await client.build_indices_and_constraints()

def get_fact_result_from_edge(edge: EntityEdge):
    return FactResult(
        uuid=edge.uuid,
        name=edge.name,
        fact=edge.fact,
        valid_at=edge.valid_at,
        invalid_at=edge.invalid_at,
        created_at=edge.created_at,
        expired_at=edge.expired_at,
    )

def get_fact_string_from_edge(edge: EntityEdge):
    return f'{edge.name}: {edge.fact}: {edge.created_at}'

def init_graphiti_prompt(message: str, context: List[ContextItem] | None, chat_history: List[ContextItem] | None) -> Dict[str, Any]:
    message = escape_markdown_special_chars(message)
    # Create the system prompt
    system_prompt_messages = []
    if context:
        for item in context:
            if item.role == "system":
                system_prompt_messages.append(
                      SystemMessage(content=item.content)
                )
    buffer = []
    if chat_history:
        for item in chat_history:
            if item.role == "user":
                buffer.append(HumanMessage(content=item.content))
            elif item.role == "assistant":
                buffer.append(AIMessage(content=item.content))
            else:
                logger.info(f"{time.time()},Get message {item}, unknown role: {item['role']}")

    new_message = HumanMessage(content=message)
    return {"messages": [*system_prompt_messages, *buffer, new_message]}

class GraphitiMessagesState(MessagesState):
    group_id: str
    user_id: str
    user_node_uuid: str

async def summary_messages_title(llm_chat, message: str):

    messages = [
        SystemMessage(
            content="你是一个会话摘要生成器。总结以下消息的主要内容，生成一个标题，不超过50字。只输出标题内容，不要输出其他内容。"
        ),
        HumanMessage(
            content=message
        )
    ]

    response = await llm_chat.ainvoke(messages)
    return response.content

async def assitant_agent(state: GraphitiMessagesState, config: RunnableConfig,  llm_chat:BaseChatModel, store:BaseStore, graphiti: ZepGraphiti, async_runner: AsyncWorker, tenant_id:str,user_id:str) -> dict:

    try:
        state['user_id'] = user_id
        state['group_id'] = tenant_id

        facts_string = None
        if len(state['messages']) > 0:
            last_message = state['messages'][-1]
            graphiti_query = f'{"assistant" if isinstance(last_message, AIMessage) else state["user_id"] + "(user)"}: {last_message.content}'

            edge_results = await graphiti.search(
                graphiti_query, 
                center_node_uuid=None, 
                search_filter= SearchFilters(
                    created_at = [[DateFilter(
                        date = datetime.now(timezone.utc) - timedelta(days=30),
                        comparison_operator=ComparisonOperator.greater_than_equal)
                    ]]
                ),
                group_ids=[state['group_id']], 
                num_results=20
            )

            facts = [get_fact_string_from_edge(edge) for edge in edge_results]
            facts_string = "\n".join(facts)
            logger.info(f"Found facts for query {graphiti_query} : \n {facts_string}")

        system_message = SystemMessage(
            content=f"""你是蓝星参谋,一个知识全面,思维开阔的企业助手，负责回答用户的问题。
            你有以下背景信息,每行是一条信息：
            名称: 事实: 发生时间
            {facts_string or '没有相关背景信息.'}"""
        )

        messages = state['messages']
        messages.insert(-1, system_message)
        response = await llm_chat.ainvoke(messages)

        episode_body = f'{state["user_id"]}(user): {state["messages"][-1].content}\nassistant: {response.content}'
        episode_title = await summary_messages_title(llm_chat, episode_body)

        async def add_messages_task(episode_title:str, episode_body:str, group_id:str):
            logger.info(f"start add message to episode {episode_title}")
            await graphiti.add_episode(
                name=episode_title,
                episode_body=episode_body,
                group_id=group_id,
                source=EpisodeType.message,
                reference_time=datetime.now(timezone.utc),
                source_description='Chat Messages',
            )
            logger.info(f"end add message to episode {episode_title}")

        await async_runner.queue.put(partial(add_messages_task, episode_title, episode_body, state['group_id']))

        return {'messages': [response]}
    
    except Exception as e:
        logger.error(f"{time.time()},Error in agent processing: {traceback.format_exc()}")
        return {"messages": [{"role": "system", "content": "处理请求时出错"}]}

async def get_graphiti_graph(model:BaseChatModel, config:RunnableConfig, store:BaseStore, graphiti: ZepGraphiti, async_worker: AsyncWorker, tenant_id:str, user_id:str) -> StateGraph:

    async def graphiti_agent(state:GraphitiMessagesState, config:RunnableConfig, model:BaseChatModel, store:BaseStore, graphiti: ZepGraphiti, async_worker: AsyncWorker, tenant_id:str, user_id:str):
        return await assitant_agent(state, config, model, store, graphiti, async_worker, tenant_id, user_id)   

    workflow = StateGraph(GraphitiMessagesState)
    workflow.add_node('generate', partial(graphiti_agent, config=config, model=model, store=store, graphiti = graphiti, async_worker=async_worker, tenant_id = tenant_id, user_id = user_id))
  
    workflow.add_edge(START, 'generate')
    workflow.add_edge('generate', END)

    return workflow

