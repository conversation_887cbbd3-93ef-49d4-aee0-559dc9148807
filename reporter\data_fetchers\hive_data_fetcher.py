# data_fetchers/hive_data_fetcher.py
from pyhive import hive
import pandas as pd
from typing import Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HiveDataFetcher:
    """
    A class to fetch data from Hive using pyhive.
    """
    
    def __init__(self, host: str, port: int = 10000, username: Optional[str] = None, 
                 database: Optional[str] = None, auth: Optional[str] = None):
        """
        Initialize the HiveDataFetcher with connection parameters.
        
        :param host: Hive server host
        :param port: Hive server port (default: 10000)
        :param username: Username for authentication (optional)
        :param database: Default database to use (optional)
        :param auth: Authentication method (e.g., 'KERBEROS', 'NONE', etc.)
        """
        self.host = host
        self.port = port
        self.username = username
        self.database = database
        self.auth = auth
        self.connection = None
    
    def connect(self):
        """
        Establish a connection to the Hive server.
        """
        try:
            self.connection = hive.Connection(
                host=self.host,
                port=self.port,
                username=self.username,
                database=self.database,
                auth=self.auth
            )
            logger.info("Successfully connected to Hive server")
        except Exception as e:
            logger.error(f"Failed to connect to Hive server: {e}")
            raise
    
    def close(self):
        """
        Close the connection to the Hive server.
        """
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Hive connection closed")
    
    def execute_query(self, sql: str) -> pd.DataFrame:
        """
        Execute a SQL query and return the results as a pandas DataFrame.
        
        :param sql: SQL query to execute
        :return: Query results as a pandas DataFrame
        """
        if not self.connection:
            self.connect()
        
        try:
            # Execute the query and fetch results
            df = pd.read_sql(sql, self.connection)
            logger.info(f"Query executed successfully, returned {len(df)} rows")
            return df
        except Exception as e:
            logger.error(f"Failed to execute query: {e}")
            raise
    
    def __enter__(self):
        """
        Context manager entry.
        """
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        Context manager exit.
        """
        self.close()

    def get_ipct_day_data(self, ymd_list: list ) -> dict:
        """
        :param ymd_list: [[year, month, day],...]
        :return: User data as a pandas DataFrame
        """
        where_str = " or ".join([f"(year=\"{ymd[0]}\" and month=\"{ymd[1]}\" and day=\"{ymd[2]}\")" for ymd in ymd_list])

        sql = f"""
            select concat(year, "-", month, "-", day) as data_date,
            size(collect_set(if(a1="2006", r3, "-"))) - 1 as uv,
            size(collect_set(if(a1="2009" and a2="1", r3, "-"))) - 1 as pay_user 
            from crius.app
            where ({where_str}) and r12!="1001"
            group by year, month, day
        """
        return self.execute_query(sql).to_dict(orient='records')
    
    def get_ipct_gen_image(self, ymd_list: list ) -> dict:
        """
        :param ymd_list: [[year, month, day],...]
        :return: User data as a dict
        """
        where_str = " or ".join([f"(year=\"{ymd[0]}\" and month=\"{ymd[1]}\" and day=\"{ymd[2]}\")" for ymd in ymd_list])

        sql = f"""
            select concat(year, "-", month, "-", day) as data_date, channel, 
            count(*) as num, count(distinct id) as uv  
            from crius.isplimg_monitorcsv
            where ({where_str}) and channel != "1001" and event = "clickplay" 
            group by year, month, day, channel
        """
        return self.execute_query(sql).to_dict(orient='records')
    
    def get_heli_day_data(self, ymd_list: list ) -> dict:
        """
        :param ymd_list: [[year, month, day],...]
        :return: User data as a dict
        """
        where_str = " or ".join([f"(year=\"{ymd[0]}\" and month=\"{ymd[1]}\" and day=\"{ymd[2]}\")" for ymd in ymd_list])

        sql = f"""
            select concat(year, "-", month, "-", day) as data_date, r100 as app_type, r12 as qudao,
            size(collect_set(if(a1="1000", r3, "-"))) - 1 as boot_uv,
            size(collect_set(if(a1="1028" and a2="3", r3, "-"))) - 1 as login_suc_uv,
            size(collect_set(if(a1="1035" and a2="11", r3, "-"))) - 1 as pay_suc_uv
            from action.bussiness
            where ({where_str}) and r12!="1001" 
            group by year, month, day, r100, r12
        """
        return self.execute_query(sql).to_dict(orient='records')

    def get_conversion_day_data(self, ymd: list ) -> dict:
        """
        :param ymd: [year, month, day]
        :return: User data as a dict
        """
        where_str = f"(year=\"{ymd[0]}\" and month=\"{ymd[1]}\" and day=\"{ymd[2]}\")"

        sql = f"""
            select concat(year, "-", month, "-", day) as data_date, 
            size(collect_set(if(a1="1000" and a2="0", r3, "-"))) - 1 as boot_uv,
            size(collect_set(if(a1="1001" and a2="0", r3, "-"))) - 1 as privacy_uv,
            size(collect_set(if(a1="1037" and a2="1", r3, "-"))) - 1 as hebao_uv,
            size(collect_set(if(a1="1028" and a2="1", r3, "-"))) - 1 as login_uv,
            size(collect_set(if(a1="1028" and a2="3", r3, "-"))) - 1 as login_suc_uv,
            size(collect_set(if(a1="1035" and a2="10", r3, "-"))) - 1 as pay_uv,
            size(collect_set(if(a1="1035" and a2="0", r3, "-"))) - 1 as confirmpay_uv,
            size(collect_set(if(a1="1035" and a2="11", r3, "-"))) - 1 as pay_suc_uv
            from action.bussiness
            where {where_str} and r12!="1001" 
            group by year, month, day
        """
        return self.execute_query(sql).to_dict(orient='records')

    def get_deliver_workflow_data(self, ymd: list ) -> dict:
        """
        :param ymd: [year, month, day, hour]
        :return: User data as a dict
        """
        where_str = f"(year=\"{ymd[0]}\" and month=\"{ymd[1]}\" and day=\"{ymd[2]}\" and hour=\"{ymd[3]}\")"
        
        sql = f"""
            select concat(year, "-", month, "-", day) as data_date,
            hour as hour, 
            ip, 

            size(collect_set(if(a1="1000" and a2="0", r3, "-"))) - 1 as boot_uv,
            size(collect_set(if(a1="1001" and a2="0", r3, "-"))) - 1 as privacy_uv,
            size(collect_set(if(a1="1002" and a2="0", r3, "-"))) - 1 as hebao_uv,
            size(collect_set(if(a1="1003" and a2="0", r3, "-"))) - 1 as login_uv,
            size(collect_set(if(a1="1004" and a2="0", r3, "-"))) - 1 as login_suc_uv,
            size(collect_set(if(a1="1005" and a2="0", r3, "-"))) - 1 as pay_uv,
            size(collect_set(if(a1="1006" and a2="0", r3, "-"))) - 1 as confirmpay_uv,
            size(collect_set(if(a1="1007" and a2="0", r3, "-"))) - 1 as pay_suc_uv
            from action.bussiness
            where {where_str} and r12!="1001" 
            group by year, month, day, hour
        """
        return self.execute_query(sql).to_dict(orient='records')
    