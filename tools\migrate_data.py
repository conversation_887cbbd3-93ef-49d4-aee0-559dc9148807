#!/usr/bin/env python
# -*- coding:utf-8 -*-

import os
import sys
import argparse
import glob
import json

# Add the parent directory to the path so we can import from notebook
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set proxy if needed
os.environ["HTTP_PROXY"] = "http://**********:1080"
os.environ["HTTPS_PROXY"] = "http://**********:1080"

from notebook.libs.langchain_lib import LangChainService

def migrate_data(input_dir):
    """Migrates data from the input directory to ChromaDB."""

    for subdir in os.listdir(input_dir):
        subdir_path = os.path.join(input_dir, subdir)

        # Skip if it's not a directory
        if not os.path.isdir(subdir_path):
            continue

        # Determine the database name
        if "em" in subdir:
            database_name = "embeddings"
            file_type = "em"
        elif "ds" in subdir:
            database_name = "datasource"
            file_type = "ds"
        else:
            print(f"Skipping {subdir}: No 'em' or 'ds' in the name.")
            continue

        print(f"Processing {subdir} as {database_name}")
        langchain_service = LangChainService(tenant_id="banyunjuhe", database_name=database_name)
        collection_name = subdir  # Use the subdirectory name as the collection name
        user_id = subdir.split("_")[0]
        file_name = subdir.split("_")[-1]
        
        # Delete the collection if it exists
        # try:
        #     langchain_service.chroma_client.delete_collection(collection_name)
        #     print(f"Deleted existing collection: {collection_name}")
        # except Exception as e:
        #     print(f"Collection {collection_name} did not exist")
        collection_exists = True
        try:
            collection = langchain_service.chroma_client.get_collection(collection_name)
            print(collection)    
        except Exception as e:
            collection_exists = False
            pass
    
        if collection_exists:
            continue
        
        texts = ""
        if file_type == "em":
            # Process embeddings.json
            json_file_path = os.path.join(subdir_path, "embeddings.json")
            try:
                texts = ""
                with open(json_file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    for item in data:
                        texts += item["text"] + "\n"
            except Exception as e:
                print(f"Error reading {json_file_path}: {e}")
        elif file_type == "ds":
            # Process doc_store.json
            json_file_path = os.path.join(subdir_path, "doc_store.json")
            try:
                with open(json_file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    for key, value in data["docstore/data"].items():
                        try:
                            text_data = json.loads(value["__data__"])
                            texts += text_data["text"] + "\n"
                        except (KeyError, json.JSONDecodeError) as e:
                            print(f"Error processing entry {key} in {json_file_path}: {e}")
                            
            except Exception as e:
                print(f"Error reading {json_file_path}: {e}")

        # Add documents to the collection
        if len(texts) > 0:
            langchain_service.add_documents(texts, user_id=user_id, file_name=file_name, collection_name=collection_name)
        else:
            print(f"No documents found in {subdir}")
            
    
def parse_args():
    desc = "Migrate data to ChromaDB"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--indir', type=str, required=True, help='Input directory containing subdirectories for each data source/embeddings.')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    input_dir = args.indir

    if not os.path.isdir(input_dir):
        print(f"Error: {input_dir} is not a valid directory.")
        sys.exit(1)

    print("Migrating data to ChromaDB...")
    migrate_data(input_dir)
    print("Migration complete.")
