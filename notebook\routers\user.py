#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from fastapi import APIRouter, Body, Depends

from ..libs.db_lib import db
from ..models.user import UserInDB
from ..schemas.user import UserInfo, UserSignUp, UserJwt
from ..utils import RespCode, hash_tool
from ..auth import signJWT, decodeJWT


router = APIRouter(prefix='/user', tags=['用户接口'])
@router.post("/signup", summary="注册接口")
def signup(form_data: UserSignUp = Body(...)):
    # 拿到前端传过来的数据
    username = form_data.username
    password = form_data.password
    # 校验数据
    pass
    # 根据用户名去数据库里面查对应的 user
    user = db.get_or_none(username)
    # 如果已经有了，就返回错误信息
    if user is not None:
        return {"msg": "当前用户名已经被占用了"}
    # 保存到数据库
    encode_pwd = hash_tool.encrypt_password(password)
    user = UserInDB(username=username, password=encode_pwd)
    db.save(user)
    # 给前端响应信息
    return RespCode.resp_ok({'msg': "ok"})

