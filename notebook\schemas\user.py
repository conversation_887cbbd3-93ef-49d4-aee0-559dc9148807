#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from pydantic import BaseModel, Field, field_validator
from ..utils.exception import PasswordMustSameException

class UserSignUp(BaseModel):
    username: str = Field(..., example="tom")
    password: str = Field(..., example="123")
    password2: str = Field(..., example="123")

    @field_validator("password2")
    def two_password_match(cls, value, values):
        if value != values['password']:
            raise PasswordMustSameException()
        
        return value

class UserLogin(BaseModel):
    username: str = Field(..., example="tom")
    password: str = Field(..., example="123")

class UserInfo(BaseModel):
    username: str
    is_superuser: bool = False
    status: bool = True

class UserJwt(BaseModel):
    uid:str = Field(..., example = "demo")
    cid:str = Field(..., example = "demo")
    app:str = Field(..., example = "demo")
    ver:str = Field(..., example = "demo")
    
class UserRequestContent(BaseModel):
    userId:str = Field(..., example = "admin")
    message:str = Field(..., example = "hello")


