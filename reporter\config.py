# config.py
from pydantic_settings import BaseSettings, SettingsConfigDict
class AppSettings(BaseSettings):
    MICROLENS_DATABASE_URL: str = ""
    FUXI_DATABASE_URL:str = ""
    HIVE_HOST: str = ""
    HIVE_PORT: int = 0
    WECOM_ROBOT_WEBHOOK_URL: str = ""

    # Email settings
    EMAIL_ADDRESS: str = "<EMAIL>"
    EMAIL_PASSWORD: str = "agent@2025"
    EMAIL_IMAP_SERVER: str = 'imap.qiye.aliyun.com'
    EMAIL_IMAP_PORT: int = 993

    # Graphiti settings for daily report agent
    openai_api_key: str = ""
    openai_base_url: str = ""
    model_name: str = ""
    embedding_model: str = ""
    embedding_dim: int = 1536
    neo4j_uri: str = ""
    neo4j_user: str = ""
    neo4j_password: str = ""
    
    qwen_api_key: str = ""
    qwen_base_url: str = ""
    qwen_model_name:str = ""
    qwen_embedding_model: str = ""
    qwen_embedding_dim: int = 1536

    model_config = SettingsConfigDict(
        env_file="./reporter/.env",
        env_file_encoding='utf-8',
        extra="allow"
    )

settings = AppSettings() # 实例化配置
