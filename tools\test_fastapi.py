import os
from typing import Async<PERSON><PERSON><PERSON>
from contextlib import asynccontextmanager
from psycopg_pool import AsyncConnectionPool

from fastapi import FastAP<PERSON>, Request, HTTPException
import traceback
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.store.postgres.aio import AsyncPostgresStore
from langchain_openai import OpenAIEmbeddings, ChatOpenAI
import uuid
from langgraph.graph import StateGraph, MessagesState, START, END
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore


POSTGRES_DB_URI="********************************************/postgres?sslmode=disable"

db_pool: AsyncConnectionPool | None = None

@asynccontextmanager
async def lifespan_event(app: FastAPI) -> AsyncIterator[dict]:
    """
    FastAPI Lifespan event handler using psycopg AsyncConnectionPool.
    Creates a database connection pool on startup and closes it on shutdown.
    """
    global db_pool
    print("Application startup: Creating database connection pool with psycopg...")
    try:
        db_pool = AsyncConnectionPool(
            min_size=1,  
            max_size=10, 
            conninfo=POSTGRES_DB_URI
        )
        print("Database connection pool created successfully with psycopg!")

        app.state.postgres_pool = db_pool
        yield 
    except Exception as e:
        print(f"Error creating database pool with psycopg: {e}")
        db_pool = None 

    print("Application shutdown: Closing database connection pool...")
    if db_pool:
        await db_pool.close()
        print("Database connection pool closed.")
    else:
        print("No database pool to close.")

def init_llm_model():
    model = ChatOpenAI(
        model="gpt-4.1-mini",
        temperature=0.7,
        max_tokens=16384,
        api_key="********************************************************************************************************************************************************************"
    )
        
    return model

def init_embeddings_model():
    model = OpenAIEmbeddings(
        openai_api_key="********************************************************************************************************************************************************************",
        model="text-embedding-3-large",
        dimensions=1536
        )
    return model 
     
app = FastAPI(lifespan=lifespan_event) 

@app.get("/abc")
async def read_root(request: Request):
    try:
        async with request.app.state.postgres_pool.connection() as conn:
            checkpoint = AsyncPostgresSaver(conn)
            store = AsyncPostgresStore(
                conn,
                index={
                "dims":1536,
                "embed":init_embeddings_model(),
                "index":"data"
            })
            
            try:
                await checkpoint.setup()
            except Exception as e:
                pass
            
            try:
                await store.setup()
            except Exception as e:
                pass
            
            model = init_llm_model()
            
            # NOTE: we're passing the Store param to the node --
            # this is the Store we compile the graph with
            async def call_model(state: MessagesState, config: RunnableConfig, *, store: BaseStore):
                # user_id = config["configurable"]["user_id"]
                # namespace = ("memories", user_id)
                # memories = await store.asearch(namespace, query=str(state["messages"][-1].content))
                # info = "\n".join([d.value["data"] for d in memories])
                # system_msg = f"You are a helpful assistant talking to the user. User info: {info}"

                # # Store new memories if the user asks the model to remember
                # last_message = state["messages"][-1]
                # if "remember" in last_message.content.lower():
                #     memory = "User name is Bob"
                #     await store.aput(namespace, str(uuid.uuid4()), {"data": memory})
                system_msg = f"You are a helpful assistant talking to the user."
                response = await model.ainvoke(
                    [{"role": "system", "content": system_msg}] + state["messages"]
                )
                return {"messages": response}


            builder = StateGraph(MessagesState)
            builder.add_node("call_model", call_model)
            builder.add_edge(START, "call_model")
            builder.add_edge("call_model", END)
            
            graph = builder.compile(checkpointer=checkpoint, store=store)
            config = {"configurable": {"thread_id": "1", "user_id": "1"}}

            # input_message = {"role": "user", "content": "Hi! Remember: my name is Bob"}
            # async for chunk in graph.astream({"messages": [input_message]}, config, stream_mode="values"):
            #     chunk["messages"][-1].pretty_print()
            
            # input_message = {"role": "user", "content": "Who am I?"}
            # async for chunk in graph.astream({"messages": [input_message]}, config, stream_mode="values"):
            #     chunk["messages"][-1].pretty_print()

            # input_message = {"role": "user", "content": "Hi! Remember: my name is Bob"}
            # async for chunk in graph.astream({"messages": [input_message]}, config, stream_mode="values"):
            #     chunk["messages"][-1].pretty_print()
            
            input_message = {"role": "user", "content": "\n与\\n的区别是什么?"}
            async for chunk in graph.astream({"messages": [input_message]}, config, stream_mode="values"):
                chunk["messages"][-1].pretty_print()

    except Exception as e:
        print(f"An unexpected error occurred: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="An internal error occurred")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("test_fastapi:app", host="0.0.0.0", port=8000, reload=True)
    