#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from langchain_openai import OpenAIEmbeddings
from langchain_chroma import Chroma
from langchain.docstore.document import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from .chromadb import ChromaDBConnection
from ..config import get_settings
import logging
from ..utils.content_helper import compute_md5
import re 

root_logger = logging.getLogger("root")

class LangChainService:
    def __init__(self, tenant_id, database_name):

        self.config = get_settings()
        self.admin_client =  ChromaDBConnection.getAdminClient()
        try:
            self.admin_client.get_tenant(tenant_id)
            
        except Exception as e:
            self.admin_client.create_tenant(tenant_id)
        try:
            self.admin_client.get_database(database_name, tenant_id)
        except Exception as e:
            self.admin_client.create_database(database_name, tenant_id)

        self.chroma_client = ChromaDBConnection.getClient(tenant_id, database_name)

        # Configure OpenAI embeddings
        self.embedding_model = OpenAIEmbeddings(
            openai_api_key=self.config.openai_api_key,
            model=self.config.embedding_model,
            dimensions=self.config.embedding_dim
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1024,
            chunk_overlap=100,
            length_function=len,
            is_separator_regex=False
            )

    def get_or_create_collection(self, collection_name="default_collection"):
        """Get or create a ChromaDB collection"""
        try:
            collection = self.chroma_client.get_or_create_collection(name=collection_name)
            return collection
        
        except Exception as e:
            root_logger.error(f"Error getting or creating collection: {str(e)}")
            raise e
    
    def create_vector_store(self, collection_name="default_collection"):
        """Create a vector store from a ChromaDB collection"""
        try:
            _ = self.get_or_create_collection(collection_name)
            vector_store = Chroma(
                client=self.chroma_client,
                collection_name=collection_name,
                embedding_function=self.embedding_model
            )
            return vector_store
        except Exception as e:
            root_logger.error(f"Error creating vector store: {str(e)}")
            raise e
    
    def add_documents(self, texts:str, user_id:str, file_name:str, collection_name="default_collection"):
        """Add documents to the vector store"""
        
        try:
            vector_store = self.create_vector_store(collection_name)
            collection = self.get_or_create_collection(collection_name)
            docs = self.text_splitter.split_text(re.sub("\\s{2,}", "\\n", texts))
    
            documents = []
            for doc in docs:
                md5id = compute_md5(doc)
                document = Document(page_content=doc, metadata = {
                    "userId": user_id,
                    "fileName": file_name,
                    "md5id":md5id,
                })
                existData = collection.get(where={"md5id": {"$eq": md5id}})
                if existData["embeddings"] == None:
                    documents.append(document)
                    
            if len(documents) > 0:
                for i in range(0, len(documents), 50):
                    vector_store.add_documents(documents[i:i+50])
            return True
        except Exception as e:
            root_logger.error(f"Error adding documents: {str(e)}")
            raise e
        
    def add_documents_with_metadata(self, texts:any, collection_name="default_collection", metadatas={}):
        """Add documents to the vector store"""
        
        try:
            vector_store = self.create_vector_store(collection_name)
            # Convert texts to documents if they aren't already
            if isinstance(texts[0], str):
                documents = [
                    Document(page_content=text, metadata=metadata or {})
                    for text, metadata in zip(texts, metadatas or [{}] * len(texts))
                ]
            else:
                documents = texts

            vector_store.add_documents(documents)

            return True
        except Exception as e:
            root_logger.error(f"Error adding documents: {str(e)}")
            raise e
    
    def query(self, query_text:str, collection_name="default_collection", k=3):
        """Query the vector store"""
        try:
            # Create vector store with the collection
            vector_store = Chroma(
                client=self.chroma_client,
                collection_name=collection_name,
                embedding_function=self.embedding_model
            )
            
            # Perform similarity search
            results = vector_store.similarity_search_with_score(query_text, k=k)
            return results
        except Exception as e:
            root_logger.error(f"Error querying vector store: {str(e)}")
            raise e 
