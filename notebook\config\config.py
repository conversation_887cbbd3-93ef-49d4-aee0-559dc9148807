#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from pathlib import Path
import os 

from pydantic_settings import BaseSettings 
from ..libs.singleton import singleton
import logging 

root_logger = logging.getLogger("root")
import dotenv

# 文档：https://pydantic-docs.helpmanual.io/usage/settings/

@singleton
class Settings(BaseSettings):

    class Config:
        extra = "allow"
        env_file_encoding = "utf-8"         
        profile = os.getenv("PROFILE")
        root_logger.info(f"use environment {profile}")
        if profile == "DEV":
            # 环境变量文件
            dotenv.load_dotenv(".env.development", override=True)
            env_file = ".env.development"
        elif profile == "TEST":
            dotenv.load_dotenv(".env.test", override=True)
            env_file = ".env.test"
        elif profile == "PROD":
            dotenv.load_dotenv(".env.production", override=True)
            env_file = ".env.production"
        else:
            dotenv.load_dotenv(".env.production", override=True)
            env_file = ".env.production"

    profile: str = "DEV"

    # debug模式
    debug: bool = True

    # 项目标题
    project_title:str = 'BlueStar Api Server Backend'
    # 项目描述
    project_description:str = '基于llama_index的agentic rag系统'
    # 项目版本
    project_version:str = '0.0.1'

    # url的前缀
    url_prefix: str = "/api"
    # host
    server_host: str = '0.0.0.0'
    server_port: int = 8766
     
    #  swagger docs 的登陆重定向地址

    # 项目根目录
    base_dir: str = Path(__file__).absolute().parent.parent.parent.absolute().as_posix()
    # 日志目录
    log_dir: str = base_dir + '/logs'
    # 静态资源
    static_dir: str = base_dir + '/static'
    static_url_prefix: str = '/static'
    # 用户上传目录
    media_dir: str = base_dir + '/media'
    media_url_prefix: str = '/media'
    # jinja2 模板目录
    jinja2_templates_dir: str = base_dir +'/notebook/templates'

    jwt_secret: str = "banyunjuhe_ai_data_&adf*^"
    jwt_algorithm: str ="HS256"

    #chromadb 设置
    chroma_server_host:str = "127.0.0.1"
    chroma_server_port:int = 8000
    chroma_client_auth_provider:str = ""
    chroma_client_auth_credentials:str = ""
    
    #postgres 数据库配置
    postgres_db_uri:str = ""
    #redis 数据库配置
    redis_db_uri:str = ""
    #api key 设置
    openai_api_key:str = ""
    qwen_api_key:str = ""
    openrouter_api_key:str = ""
    openai_base_url:str = ""
    qwen_base_url:str = ""
    openrouter_base_url:str = ""

    #embedding 配置
    embedding_model:str = "text-embedding-3-large"
    embedding_dim:str = 1536

    #ollama 设置
    ollama_base_url: str = ""
    deepseek_model_quant: str = ""
    #代理 设置
    http_proxy:str = ""
    https_proxy:str = ""

    #可用模型配置
    openai_models:str = ""
    qwen_models:str = ""
    openrouter_models:str = ""
    
    #搜索工具
    tavily_api_key:str = ""
    
    prompt_template_agent:str = "./prompts/prompt_template_agent.txt"
    prompt_template_generate:str = "./prompts/prompt_template_generate.txt"
    prompt_template_grade:str = "./prompts/prompt_template_grade.txt"
    prompt_template_rewrite:str = "./prompts/prompt_template_rewrite.txt"        
    prompt_template_assistant:str = "./prompts/prompt_template_assistant.txt"    
    prompt_template_recommend:str = "./prompts/prompt_template_recommend.txt"
    prompt_template_router:str = "./prompts/prompt_template_router.txt"
    
    oss_endpoint:str = "oss-rg-china-mainland.aliyuncs.com"
    oss_secretkey:str = "LTAI5tBWs1EknMeactnaxhHK"
    oss_secretpass:str = "******************************"
    oss_bucket:str = "byjh-ai-data"
    python_cmd:str = "python"
    mcp_host:str = "**********"
    
    neo4j_uri:str = "bolt://************:7687"
    neo4j_user:str = "neo4j"
    neo4j_password:str = "neo4j_data"

    qwen_embedding_model:str = "text-embedding-v4"
    qwen_embedding_dim:str = "1536"
    qwen_model_name:str = ""
