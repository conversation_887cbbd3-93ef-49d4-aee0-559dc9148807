import base64
from PIL import Image
from io import BytesIO

def base64_to_image(data_url):
    # 提取 Base64 编码部分
    _, encoded = data_url.split(',', 1)
    image_data = base64.b64decode(encoded)
    # 使用 BytesIO 打开图像
    image = Image.open(BytesIO(image_data))
    return image

# 进行图像大小的缩放操作，保留长宽比，最长边最大尺寸为 512
def resize_image(image:Image, max_size=512):
    width, height = image.size
    if width > height:
        new_width = max_size
        new_height = int((max_size / width) * height)
    else:
        new_height = max_size
        new_width = int((max_size / height) * width)

    resized_image = image.resize((new_width, new_height), Image.Resampling.BICUBIC)
    return resized_image

# 将图片转换为 Base64 字符串并拼接成 data URL
def image_to_base64(image):
    image_byte_arr = BytesIO()
    image.save(image_byte_arr, format="webp")
    image_byte_arr = image_byte_arr.getvalue()
    base64_encoded_data = base64.b64encode(image_byte_arr).decode()
    data_url = f"data:webp;base64,{base64_encoded_data}"
    return data_url

def scale_image(image_str):
    image_png = base64_to_image(image_str)
    image_png_resized = resize_image(image_png)
    data_url_png_resized = image_to_base64(image_png_resized)
    return data_url_png_resized

