#!/usr/bin/env python
# -*- coding:utf-8 -*-

from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
import asyncio
from langchain_openai import ChatOpenAI
from PIL import Image
from io import BytesIO
import base64
from langchain_core.messages import ToolMessage, AIMessageChunk
import json

import os
os.environ["NO_PROXY"] = "192.168.3.56"

model = ChatOpenAI(model="gpt-4.1-mini", api_key="********************************************************************************************************************************************************************")

async def main():

    client = MultiServerMCPClient(
        {
            "mcp_server": {
                "url": "http://192.168.3.56:8766/mcp_server/mcp/",
                "transport": "streamable_http"
            }
        }
    ) 

    tools = await client.get_tools()
    agent = create_react_agent(model, tools)
    # math_response = await agent.ainvoke({"messages": "what's (3 + 5) x 12?"})
    # print(math_response)
    # weather_response = await agent.ainvoke({"messages": "what is the weather in nyc?"})
    # print(weather_response)

    async for chunk in agent.astream({"messages": "画一幅图，一个摩登女孩在走T台秀"}, stream_mode="messages"):
        c = chunk[0].content
        data = ""
        if type(chunk[0]) == ToolMessage:
            if chunk[0].name == "tavily_search":
                datas = json.loads(c)
                events = []
                for rs in datas["results"]:
                    events.append(f"event:search\ndata:\"{rs['url']}\"")
                data = "\n\n".join(events) + "\n\n"
                
            # RAG的结果不向用户展示
            if chunk[0].name == "retrieve":
                data = ""
            if chunk[0].name == "create_image":
                img_data = chunk[0].artifact[0].data
                img_mime = chunk[0].artifact[0].mimeType
                data = f'event:image\ndata: "data:{img_mime};base64,{img_data}"\n\n'
                
        elif type(chunk[0]) == AIMessageChunk:
            if chunk[1]['langgraph_node'] in ['grade_documents', "rewrite"]: 
                data = ""
                
            if chunk[1]['langgraph_node'] in ['assistant', 'agent'] and c != "":
                data = f'event:think\ndata: "{c}"\n\n'
            
            if chunk[1]['langgraph_node'] == "recommend_question" and c != "":
                data = f'event:recommend\ndata: "{c}"\n\n'
                
        if data != "":
            print(data)
            
asyncio.run(main())
