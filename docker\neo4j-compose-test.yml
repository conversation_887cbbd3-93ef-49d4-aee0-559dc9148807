networks:
    compose:
        driver: bridge
        ipam:
            driver: default
            config:
            - subnet: **********/24
              gateway: **********
services:
  neo4j:
    image: neo4j:5.26.2
    container_name: neo4j
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget -qO- http://localhost:${NEO4J_PORT:-7474} || exit 1",
        ]
      interval: 1s
      timeout: 10s
      retries: 10
      start_period: 3s
    ports:
      - "7474:7474" # HTTP
      - "${NEO4J_PORT:-7687}:${NEO4J_PORT:-7687}" # Bolt
    volumes:
      - neo4j_data:/data
    environment:
      - NEO4J_AUTH=neo4j/neo4j_data
    networks:
      compose:
        ipv4_address: **********
    restart: unless-stopped

volumes:
  neo4j_data:
