import requests
import base64
import os
import json

print(os.environ.get('HTTP_PROXY'))
print(os.environ.get('HTTPS_PROXY'))
print(os.environ.get('NO_PROXY'))

excel_file_path = 'tools/test.xlsx'
api_endpoint = 'http://127.0.0.1:8766/api/embedding'

try:
    with open(excel_file_path, 'rb') as f:
        excel_data = f.read()
    content = str(base64.b64encode(excel_data), encoding="utf-8")
    data = {'excel': content, "userId":"test", "dsName":"test.xlsx"}
    response = requests.post(api_endpoint, data=json.dumps(data))

    print(f"Status Code: {response.status_code}")
    print(f"Response Body: {response.text}")

except FileNotFoundError:
    print(f"Error: The file {excel_file_path} was not found.")
except Exception as e:
    print(f"An error occurred: {e}")
