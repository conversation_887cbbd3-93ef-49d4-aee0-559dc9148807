# api-llamaindex

#### 介绍
基于llamaindex python版本的agentic rag api实现

#### 软件架构
uvicorn + fastapi + langchain + vector_store + workflow + crontab job? 

#### 安装教程

```bash
    pip install -r requirements.txt 
    #设置环境变量
    $env:PROFILE="DEV"
```

#### 使用说明

```bash
    #chromdb 安装
    #!/bin/bash
    #docker run --rm --entrypoint htpasswd httpd:2 -Bbn admin Smile5More#apper1 > server.htpasswd
    #docker run -d --name chromadb --env-file /root/chromadb/.chroma_env -v /root/chromadb:/chroma/chroma -p 8000:8000 chromadb/chroma:0.6.2 
```

```bash
    #.chroma_env的内容
    CHROMA_SERVER_AUTHN_CREDENTIALS_FILE=/chroma/chroma/server.htpasswd
    CHROMA_SERVER_AUTHN_PROVIDER=chromadb.auth.basic_authn.BasicAuthenticationServerProvider
```

```bash
    #chromadb 数据结构设计
    #原始next-llamaindex数据结构
    #userId_{em:ds}_(md5(filename))

    #对应到chromadb 
    # tenant => userId
    # database => {em|ds}_md5(filename)
    # collenction => chuncks(文档块)
     
```