import time
import os
import oss2
from datetime import datetime
import traceback

# OSS Configuration
OSS_ENDPOINT = "http://oss-cn-beijing.aliyuncs.com"  # Public endpoint instead of internal
OSS_BUCKET = "cdn-buckeet"
OSS_CDN_PREFIX = "https://cdn.banyunjuhe.com"
OSS_SECRET_ID = "LTAI5tRFHhUZAyjm3ZJsMAuB"
OSS_SECRET_KEY = "******************************"
OSS_BASE_PATH = "reporter/charts"

def get_now():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def upload_oss(img_path: str, year: str, month: str, day: str):
    """
    Upload an image to OSS storage
    
    Args:
        img_path (str): Path to the local image file
        year (str): Year for organizing OSS path
        month (str): Month for organizing OSS path
        day (str): Day for organizing OSS path
        
    Returns:
        str or None: CDN URL of uploaded image or None if failed
    """
    # Check if file exists
    if not os.path.exists(img_path):
        print(f"{get_now()} 文件不存在: {img_path}")
        return None
    
    timestamp = int(datetime.now().timestamp() * 1000)
    ext = os.path.splitext(img_path)[1]
    oss_path = f"{OSS_BASE_PATH}/{year}/{month}/{day}/{timestamp}{ext}"
    upload_time = 0
    upload_status = False
    
    print(f"{get_now()} 开始上传文件: {img_path}")
    print(f"{get_now()} OSS路径: {oss_path}")
    
    while True:
        if upload_time > 5 and upload_status == False:
            print(f"{get_now()} 上传{img_path} 到 oss://{OSS_BUCKET}/{oss_path} 失败.请及时关注.")
            break
        if upload_status == True:
            break
        try:
            upload_time += 1
            print(f"{get_now()} 第{upload_time}次上传尝试")
            
            # Create OSS bucket instance
            auth = oss2.Auth(OSS_SECRET_ID, OSS_SECRET_KEY)
            bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET)
            
            # Upload file directly (correct way, not base64 encoded)
            result = bucket.put_object_from_file(oss_path, img_path)
            
            if result.status == 200:
                print(f"{get_now()} 上传成功")
                upload_status = True
            else:
                print(f"{get_now()} 上传返回状态码: {result.status}")
                upload_status = False
                
        except Exception as e:
            print(f"{get_now()} 上传失败，第{upload_time}次重试: {str(e)}")
            traceback.print_exc()
            upload_status = False
            time.sleep(5)
    
    if upload_status == True:
        cdn_url = f"{OSS_CDN_PREFIX}/{oss_path}"
        print(f"{get_now()} 上传完成，CDN地址: {cdn_url}")
        return cdn_url
    else:
        return None
