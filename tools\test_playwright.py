#!/usr/bin/env python
# -*- coding:utf-8 -*-

# from langchain_community.document_loaders import PlaywrightURLLoader
# url = "https://mp.weixin.qq.com/s/Xdlfw4Pc0XCRnJs3DEOdUQ"
# loader = PlaywrightURLLoader(urls=[url], remove_selectors=["header", "footer"])
# data = loader.load()
# text = ""
# for d in data:
#     text += d.page_content
# print(text)

import asyncio
import fastapi,uvicorn
from playwright.async_api import async_playwright
from langchain_community.document_loaders import PlaywrightURLLoader

class FS131VIAPI:
    def __init__(self):
        self.cookie = None
    async def fetch(self, params):
        await self.login_for_cookie()

    async def login_for_cookie(self):
        # async with async_playwright() as p:
        #     browser = await p.chromium.launch(headless=False)
        #     context = await browser.new_context()
        #     page = await context.new_page()
        #     await page.goto("https://cn.bing.com/")
        #     print(await page.title())
        #     print(await context.cookies())
        #     self.cookie = await context.cookies()
        #     await context.close()

        loader = PlaywrightURLLoader(urls=["https://baijiahao.baidu.com/s?id=1831178429049075725&wfr=spider&for=pc"], headless=True)
        data = await loader.aload()
        print(data)
        return data
    
fs131vi_api = FS131VIAPI()
app = fastapi.FastAPI()

@app.get("/test")
async def index():
    await fs131vi_api.fetch({"SERVICE_IDS": "xxx"})

if __name__ == "__main__":

    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    uvicorn.run(
        "test_playwright:app",
        host="0.0.0.0",
        reload=False,
        workers=1,
        port=8002
    )
    