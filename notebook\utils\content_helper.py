#!/usr/bin/env python
# -*- coding:utf-8 -*-

import hashlib
import requests
import PyPDF2
import io
import base64

import logging
import pandas as pd
from ..config import get_settings

from openai import OpenAI
import oss2
import uuid
from datetime import datetime
import subprocess
from ..config import get_settings
import os

settings = get_settings()

root_logger = logging.getLogger("root")

# 用于计算文件名的MD5值
def compute_md5(filename: str) -> str:
    """计算文件的 MD5 值"""
    hash_md5 = hashlib.md5()
    hash_md5.update(filename.encode())
    return hash_md5.hexdigest()

def extract_text_from_pdf(pdf_file: str) -> str:
    """Extract text content from a PDF file"""
    try:
        # Create a PDF reader object
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(base64.b64decode(pdf_file)))
        
        # Extract text from all pages
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text() + "\n"
        
        return text.strip()
    except Exception as e:
        root_logger.error(f"Error extracting text from PDF: {str(e)}")
        raise e

def extract_text_from_excel(excel_file: str) -> str:
    """Extract CSV content from an Excel file."""
    try:
        excel_bytes = base64.b64decode(excel_file)
        
        # Use BytesIO to handle the Excel data in memory
        excel_file_stream = io.BytesIO(excel_bytes)
        try:
            
        # Read all sheets from the Excel file
            xls = pd.ExcelFile(excel_file_stream, engine="openpyxl")
        except Exception as e:
            xls = pd.ExcelFile(excel_file_stream, engine="xlrd")

        csv_content = ""
        for sheet_name in xls.sheet_names:
            df = xls.parse(sheet_name)
            csv_content += df.to_csv(index=False, sep="\t") + "\n"
        
        return csv_content.strip()
    
    except Exception as e:
        root_logger.error(f"Error extracting CSV from Excel: {str(e)}")
        raise e
    
def push_to_oss(oss_prefix, data, file_name):
    settings = get_settings()
    image_filename = str(uuid.uuid4()) + "_" + file_name
    oss_path = f"{oss_prefix}/{image_filename}"
    
    bucket = oss2.Bucket(oss2.Auth(settings.oss_secretkey, settings.oss_secretpass), settings.oss_endpoint, settings.oss_bucket)
    bucket.put_object(oss_path, data)
    return oss_path

def extract_text_from_image(image_file: str, file_name: str, desc:str) -> str:
    """Extract text from an image file using Qwen-VL."""
    try:
        image_bytes = base64.b64decode(image_file)
        date_prefix = datetime.now().strftime("qwen/%Y/%m/%d")
        oss_path = push_to_oss(date_prefix, image_bytes, file_name)
        settings = get_settings()
        client = OpenAI(
            api_key = settings.qwen_api_key,
            base_url = settings.qwen_base_url,
            timeout=30  # 增加超时设置
        )
        img_url = f"https://{settings.oss_bucket}.{settings.oss_endpoint}/{oss_path}"
        messages = [{
            "role": "user",
            "content": [
                {"type": "text", "text": "{desc}"},
                {"type": "image_url", 
                 "image_url": {
                     "url": img_url
                 }}
            ]
        }]

        response = client.chat.completions.create(
            model="qwen2.5-vl-72b-instruct",
            messages=messages,
            max_tokens=1024,
            temperature=0.7
        )

        if response.choices:
            result = response.choices[0].message.content.strip()
            return result.replace("2b (nier:automata)", "").strip()
        return "图片识别遇到了问题。"
    except Exception as e:
        root_logger.error(f"Error extracting text from image: {str(e)}")
        raise e

def get_url_content(url: str) -> str:
    """Fetch and return the content of a URL"""
    try:
        result = subprocess.run(
            [settings.python_cmd, "tools/spider_with_playwright.py", url],
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        text = result.stdout
        return text
    
    except Exception as e:
        root_logger.error(f"Error fetching URL content: {str(e)}")
        raise e
    