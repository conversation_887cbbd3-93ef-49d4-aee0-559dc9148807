#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import chromadb
from .singleton import singleton

from chromadb.config import Settings
from ..config import get_settings
import os 

class ChromaDBConnection(object):
    
    @staticmethod
    def getClient(tenant_id:str, database_name:str):
        config = get_settings()
        os.environ["HTTP_PROXY"] = config.http_proxy
        os.environ["HTTPS_PROXY"] = config.https_proxy
        
        client = chromadb.HttpClient(
            host=config.chroma_server_host,
            port=config.chroma_server_port,
            settings=Settings(
                chroma_client_auth_provider=config.chroma_server_authn_provider,
                chroma_client_auth_credentials=config.chroma_server_authn_credentials,
                chroma_server_ssl_enabled=False,
                anonymized_telemetry=False
            ),
            tenant=tenant_id,
            database=database_name
        )
        return client
    @staticmethod
    async def getAsyncClient(tenant_id:str, database_name:str):
        config = get_settings()
        os.environ["HTTP_PROXY"] = config.http_proxy
        os.environ["HTTPS_PROXY"] = config.https_proxy
        
        client = await chromadb.AsyncHttpClient(
            host=config.chroma_server_host,
            port=config.chroma_server_port,
            settings=Settings(
                chroma_client_auth_provider=config.chroma_server_authn_provider,
                chroma_client_auth_credentials=config.chroma_server_authn_credentials,
                chroma_server_ssl_enabled=False,
                anonymized_telemetry=False
            ),
            tenant=tenant_id,
            database=database_name
        )
        return client
    
    @staticmethod
    def getAdminClient():

        config = get_settings()
        os.environ["HTTP_PROXY"] = config.http_proxy
        os.environ["HTTPS_PROXY"] = config.https_proxy
        
        adminClient= chromadb.AdminClient(Settings(
            chroma_api_impl="chromadb.api.fastapi.FastAPI",
            chroma_server_host=config.chroma_server_host,
            chroma_server_http_port=config.chroma_server_port,
            chroma_client_auth_provider=config.chroma_server_authn_provider,
            chroma_client_auth_credentials=config.chroma_server_authn_credentials,
            chroma_server_ssl_enabled=False,
            anonymized_telemetry=False
            ))
        return adminClient
    
        


