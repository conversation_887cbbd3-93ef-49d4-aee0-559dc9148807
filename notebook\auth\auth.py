# app/auth/auth_handler.py

import time
from typing import Dict
import jwt

from ..config import get_settings
from ..utils import RespCode
from ..schemas import user

settings = get_settings()

def signJWT(user: user.UserJwt):
    payload = {
        "uid": user.uid,
        "app": user.app,
        "cid": user.cid,
        "ver": user.ver,
        "expires": time.time() + 3600
    }
    token = jwt.encode(payload, settings.jwt_secret, algorithm=settings.jwt_algorithm)
    return token

def decodeJWT(token: str):
    try:
        decoded_token = jwt.decode(token, settings.jwt_secret, algorithms=[settings.jwt_algorithm])
        return decoded_token
    except:
        return None
