import re
import logging
import async<PERSON>
from datetime import datetime
from typing import List, Dict, Any, Optional

from utils.email_receiver import Em<PERSON>R<PERSON><PERSON>ver
from utils.crypt_tool import decrypt_banyun_java
from utils.graphiti_lib import GraphitiSettings, get_graphiti, AsyncWorker
from data_fetchers.mysql_data_fetcher import SQLDataFetcher
from functools import partial
from config import settings

from graphiti_core.nodes import EpisodeType
from datetime import timezone
import sys

print(settings)
sys.exit(1)

logger = logging.getLogger(__name__)

class DailyReportAgent:
    """
    工作日报处理Agent
    每天执行一次，处理工作日报邮件并发送给蓝星参谋
    """
    
    def __init__(self):
        """初始化Agent"""
        self.email_receiver = EmailReceiver(
            email_address=settings.EMAIL_ADDRESS,
            password=settings.EMAIL_PASSWORD,
            imap_server=settings.EMAIL_IMAP_SERVER,
            port=settings.EMAIL_IMAP_PORT
        )
        self.sql_fetcher = SQLDataFetcher(settings.FUXI_DATABASE_URL)
        self.async_worker = AsyncWorker()

        # Graphiti设置
        self.graphiti_settings = GraphitiSettings(
            openai_api_key=settings.openai_api_key,
            openai_base_url=settings.openai_base_url,
            model_name=settings.model_name,
            embedding_model_name=settings.embedding_model_name,
            embedding_dim=settings.embedding_dim,
            neo4j_uri=settings.neo4j_uri,
            neo4j_user=settings.neo4j_user,
            neo4j_password=settings.neo4j_password
        )
        
        logger.info("DailyReportAgent initialized")
    
    def extract_phone_from_signature(self, content: str) -> Optional[str]:
        """
        从邮件内容的签名中提取手机号
        
        Args:
            content: 邮件内容
            
        Returns:
            提取到的手机号，如果没有找到返回None
        """
        # 匹配11位手机号的正则表达式
        phone_pattern = r'1[3-9]\d{9}'
        matches = re.findall(phone_pattern, content)
        
        if matches:
            # 返回最后一个匹配的手机号（通常在签名中）
            return matches[-1]
        
        return None
    
    def find_user_id_by_phone(self, phone: str, star_users: List[Dict]) -> Optional[str]:
        """
        通过手机号查找用户ID
        
        Args:
            phone: 明文手机号
            star_users: 从get_star_user_phone获取的用户列表
            
        Returns:
            用户ID，如果没有找到返回None
        """
        for user in star_users:
            try:
                # 解密用户的手机号
                decrypted_phone = decrypt_banyun_java(user['phone'])
                if decrypted_phone == phone:
                    return str(user['id'])
            except Exception as e:
                logger.warning(f"Failed to decrypt phone for user {user['id']}: {str(e)}")
                continue
        
        return None
    
    def filter_daily_report_emails(self, emails: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤出工作日报邮件
        
        Args:
            emails: 邮件列表
            
        Returns:
            工作日报邮件列表
        """
        daily_reports = []
        
        for email in emails:
            subject = email.get('subject', '').lower()
            # 检查标题是否包含"工作日报"
            if '工作日报' in subject:
                daily_reports.append(email)
                logger.info(f"Found daily report email: {email['subject']}")
        
        return daily_reports
    
    async def send_to_lanxing_canmou(self, user_id: str, report_content: str, email_subject: str):
        """
        将工作日报发送给蓝星参谋
        
        Args:
            user_id: 用户ID
            report_content: 工作日报内容
            email_subject: 邮件标题
        """
        try:
            async with get_graphiti(self.graphiti_settings) as graphiti:
                                # 生成episode标题
                episode_title = f"{email_subject}"
                
                # 构建消息内容
                message_content = f"{user_id}(user):{email_subject}\n\n{report_content}"
                


                async def add_daily_report(title:str, message:str, group_id:str, user_id:str):
                    # 添加到graphiti
                    await graphiti.add_episode(
                        name=title,
                        episode_body=message,
                        group_id=group_id,  # 使用公司组ID
                        source=EpisodeType.message,
                        reference_time=datetime.now(timezone.utc),
                        source_description=f'Daily Report from user {user_id}',
                    )
                    logger.info(f"Successfully sent daily report to 蓝星参谋 for user {user_id}")
                
                await self.async_worker.queue.put(partial(add_daily_report, episode_title, message_content, "banyunjuhe", user_id)) 

        except Exception as e:
            logger.error(f"Failed to send daily report to 蓝星参谋: {str(e)}")
            raise
    
    async def process_daily_reports(self):
        """
        处理工作日报的主要方法
        """
        try:
            logger.info("Starting daily report processing")
            
            # 1. 获取昨天的邮件
            emails = self.email_receiver.get_emails_by_date_range()
            logger.info(f"Retrieved {len(emails)} emails from yesterday")
            
            if not emails:
                logger.info("No emails found from yesterday")
                return
            
            # 2. 过滤出工作日报邮件
            daily_reports = self.filter_daily_report_emails(emails)
            logger.info(f"Found {len(daily_reports)} daily report emails")
            
            if not daily_reports:
                logger.info("No daily report emails found")
                return
            
            # 3. 获取用户手机号列表
            star_users = self.sql_fetcher.get_star_user_phone()
            logger.info(f"Retrieved {len(star_users)} star users")
            
            # 4. 处理每个工作日报邮件
            processed_count = 0
            for report_email in daily_reports:
                try:
                    # 提取手机号
                    phone = self.extract_phone_from_signature(report_email['content'])
                    if not phone:
                        logger.warning(f"No phone number found in email: {report_email['subject']}")
                        continue
                    
                    logger.info(f"Extracted phone: {phone}")
                    
                    # 查找用户ID
                    user_id = self.find_user_id_by_phone(phone, star_users)
                    if not user_id:
                        logger.warning(f"No user found for phone: {phone}")
                        continue
                    
                    logger.info(f"Found user ID: {user_id} for phone: {phone}")
                    
                    # 发送给蓝星参谋
                    await self.send_to_lanxing_canmou(
                        user_id=user_id,
                        report_content=report_email['content'],
                        email_subject=report_email['subject']
                    )
                    
                    processed_count += 1
                    logger.info(f"Successfully processed daily report for user {user_id}")
                    
                except Exception as e:
                    logger.error(f"Error processing email {report_email['subject']}: {str(e)}")
                    continue
            
            logger.info(f"Daily report processing completed. Processed {processed_count} reports")
            
        except Exception as e:
            logger.error(f"Error in daily report processing: {str(e)}")
            raise
    
    async def run_daily(self):
        """
        每日执行的主方法
        """
        try:
            await self.process_daily_reports()
        except Exception as e:
            logger.error(f"Daily run failed: {str(e)}")
            raise

async def main():
    """Agent 收集运营日报并发送给蓝星参谋"""
    agent = DailyReportAgent()
    await agent.run_daily()

if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    asyncio.run(main())
