import sys
import json 
import os

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import re
import logging
import itertools
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from utils.email_receiver import EmailReceiver
from utils.crypt_tool import decrypt_banyun_java
from utils.graphiti_lib import GraphitiSettings, get_graphiti
from data_fetchers.mysql_data_fetcher import SQLDataFetcher
from graphiti_core.nodes import EpisodeType
from datetime import timezone

import traceback
from config import settings

logger = logging.getLogger(__name__)

class DailyReportAgent:
    """
    工作日报处理Agent
    每天执行一次，处理工作日报邮件并发送给蓝星参谋
    """
    
    def __init__(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None):
        """
        初始化Agent

        Args:
            start_date: 开始日期，如果不指定则默认为昨天
            end_date: 结束日期，如果不指定则默认为昨天
        """
        self.start_date = start_date
        self.end_date = end_date

        # 如果没有指定日期，默认使用昨天
        if self.start_date is None or self.end_date is None:

            self.start_date = self.start_date or datetime.now() - timedelta(days=1)
            self.end_date = self.end_date or datetime.now()

        self.email_receiver = EmailReceiver(
            email_address=settings.EMAIL_ADDRESS,
            password=settings.EMAIL_PASSWORD,
            imap_server=settings.EMAIL_IMAP_SERVER,
            port=settings.EMAIL_IMAP_PORT
        )
        self.sql_fetcher = SQLDataFetcher(settings.FUXI_DATABASE_URL)

        # Graphiti设置
        self.graphiti_settings = GraphitiSettings(
            openai_api_key=settings.qwen_api_key,
            openai_base_url=settings.qwen_base_url,
            model_name=settings.qwen_model_name,
            embedding_model=settings.qwen_embedding_model,
            embedding_dim=settings.qwen_embedding_dim,
            neo4j_uri=settings.neo4j_uri,
            neo4j_user=settings.neo4j_user,
            neo4j_password=settings.neo4j_password
        )

        logger.info(f"DailyReportAgent initialized with date range: {self.start_date} to {self.end_date}")
    
    def extract_phone_from_signature(self, content: str) -> Optional[str]:
        """
        从邮件内容的签名中提取手机号
        
        Args:
            content: 邮件内容
            
        Returns:
            提取到的手机号，如果没有找到返回None
        """
        # 匹配11位手机号的正则表达式
        phone_pattern = r'1[3-9]\d{9}'
        matches = re.findall(phone_pattern, content)
        
        if matches:
            # 返回最后一个匹配的手机号（通常在签名中）
            return matches[-1]
                
        return None
    
    def filter_daily_report_emails(self, emails: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤出工作日报邮件
        
        Args:
            emails: 邮件列表
            
        Returns:
            工作日报邮件列表
        """
        daily_reports = []
        
        for email in emails:
            subject = email.get('subject', '').lower()
            # 检查标题是否包含"工作日报"
            if '日报' in subject:
                daily_reports.append(email)
                logger.info(f"Found daily report email: {email['subject']}")
        
        return daily_reports
    
    async def add_user_edges(self, users: dict):
        """
        为用户添加关系边
        
        Args:
            users: 用户手机号 -> user_id 的map
        """

        user_nodes = []
        async with get_graphiti(self.graphiti_settings) as graphiti:
            for _, user_id in users.items():
                try:
                    user_node = await graphiti.get_entity_node(str(user_id))
                    if not user_node:
                        user_node = await graphiti.save_entity_node(
                            name=f"{user_id}(user)",
                            uuid=str(user_id),
                            group_id="banyunjuhe"
                        )
                        logger.info(f"Successfully added user node for user {user_id}")
                    else:
                        logger.info(f"User node already exists for user {user_id}")

                    user_nodes.append(user_node)
                except Exception as e:
                    logger.error(f"Failed to add user edge for user {user_id}: {traceback.format_exc()}")

            # Concurrently add entity edges between all pairs of users
            add_edge_tasks = []
            # Create all unique pairs of user nodes
            node_pairs = list(itertools.permutations(user_nodes, 2))
            edges_cache = {}
            for source_node, target_node in node_pairs:
                if source_node.uuid not in edges_cache:
                    edges = await graphiti.get_entity_edges_by_node_uuid(source_node.uuid)
                    edges_cache[source_node.uuid] = edges
                else:
                    edges = edges_cache[source_node.uuid]

                exists = False
                for edge in edges:
                    if edge.target_node_uuid == target_node.uuid:
                        exists = True
                        break
                if not exists:
                    add_edge_tasks.append(graphiti.add_entity_edge(
                        name="USER_RELATION",
                        fact=f"{source_node.name} is a colleague of {target_node.name}",
                        source_node=source_node,
                        target_node=target_node,
                        group_id="banyunjuhe"
                    ))

            if len(add_edge_tasks) > 0:
                results = await asyncio.gather(*add_edge_tasks, return_exceptions=True)
                for (source_node, target_node), result in zip(node_pairs, results):
                    if isinstance(result, Exception):
                        logger.error(f"Failed to add user edge for user {source_node.uuid} -> {target_node.uuid}: {result}")
                    else:
                        logger.info(f"Successfully added user edge for user {source_node.uuid} -> {target_node.uuid}")

    async def send_to_lanxing_canmou(self, user_id: str, report_content: str, email_subject: str):
        """
        将工作日报发送给蓝星参谋
        
        Args:
            user_id: 用户ID
            report_content: 工作日报内容
            email_subject: 邮件标题
        """
        try:
            async with get_graphiti(self.graphiti_settings) as graphiti:
                # episode标题
                episode_title = f"{email_subject}"
                # 构建消息内容
                message_content = f"{user_id}(user):{email_subject}\n\n{report_content}"
            
                # 添加到graphiti
                await graphiti.add_episode(
                    name=episode_title,
                    episode_body=message_content,
                    group_id="banyunjuhe", 
                    source=EpisodeType.message,
                    reference_time=datetime.now(timezone.utc),
                    source_description=f'Daily Report from user {user_id}',
                )
                logger.info(f"Successfully sent daily report to 蓝星参谋 for user {user_id}")
            
        except Exception as e:
            logger.error(f"Failed to send daily report to 蓝星参谋: {traceback.format_exc()}")
            raise
    
    async def process_daily_reports(self):
        """
        处理工作日报的主要方法
        """
        try:
            logger.info(f"Starting daily report processing for date range: {self.start_date} to {self.end_date}")

            # 1. 获取指定日期范围的邮件
            emails = self.email_receiver.get_emails_by_date_range(self.start_date, self.end_date)
            # emails = self.email_receiver.get_yesterday_emails()
            
            logger.info(f"Retrieved {len(emails)} emails from {self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}")
            
            if not emails:
                logger.info(f"No emails found in date range {self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}")
                return
            
            # 2. 过滤出工作日报邮件
            daily_reports = self.filter_daily_report_emails(emails)
            logger.info(f"Found {len(daily_reports)} daily report emails")
            
            if not daily_reports:
                logger.info("No daily report emails found")
                return
            
            # 3. 获取用户手机号列表
            star_users = self.sql_fetcher.get_star_user_phone()
            logger.info(f"Retrieved {len(star_users)} star users")
            
            user_phone_map = {decrypt_banyun_java(user['phone']): user['id'] for user in star_users}  
            await self.add_user_edges(user_phone_map)

        
            # 4. 处理每个工作日报邮件
            processed_count = 0
            for report_email in daily_reports:
                try:
                    
                    logger.info(f"Email content: {report_email['content']}")
                    # 提取手机号
                    phone = self.extract_phone_from_signature(report_email['content'])
                    if not phone:
                        logger.warning(f"No phone number found in email: {report_email['subject']}")
                        continue
                    
                    logger.info(f"Extracted phone: {phone}")
                    
                    # 查找用户ID
                    user_id = user_phone_map.get(phone, None)
                    if not user_id :
                        logger.warning(f"No user found for phone: {phone}")
                        continue
                    
                    logger.info(f"Found user ID: {user_id} for phone: {phone}")
                    
                    # 发送给蓝星参谋
                    await self.send_to_lanxing_canmou(
                        user_id=user_id,
                        report_content=report_email['content'],
                        email_subject=report_email['subject']
                    )
                    
                    processed_count += 1
                    logger.info(f"Successfully processed daily report for user {user_id}")
                    
                except Exception as e:
                    logger.error(f"Error processing email {report_email['subject']}: {traceback.format_exc()}")
                    continue
            
            logger.info(f"Daily report processing completed. Processed {processed_count} reports")
            
        except Exception as e:
            logger.error(f"Error in daily report processing: {traceback.format_exc()}")
            raise
    
    async def run_daily(self):
        """
        每日执行的主方法
        """
        try:
            await self.process_daily_reports()

        except Exception as e:
            logger.error(f"Daily run failed: {traceback.format_exc()}")
            raise

def parse_date_string(date_str: str) -> datetime:
    """
    解析日期字符串为datetime对象

    Args:
        date_str: 日期字符串，支持格式：YYYYMMDD

    Returns:
        datetime对象
    """
    try:
        return datetime.strptime(date_str, "%Y%m%d")
    except ValueError:
        raise ValueError(f"Invalid date format: {date_str}. Expected format: YYYYMMDD")

async def main(start_date: Optional[datetime] = None, end_date: Optional[datetime] = None):
    """
    Agent 收集运营日报并发送给蓝星参谋

    Args:
        start_date: 开始日期，格式为datetime对象
        end_date: 结束日期，格式为datetime对象
    """
    agent = DailyReportAgent(start_date=start_date, end_date=end_date)
    await agent.run_daily()

async def main_with_date_strings(start_date_str: Optional[str] = None, end_date_str: Optional[str] = None):
    """
    使用日期字符串参数的主函数

    Args:
        start_date_str: 开始日期字符串，格式：YYYY-MM-DD
        end_date_str: 结束日期字符串，格式：YYYY-MM-DD
    """
    start_date = None
    end_date = None

    if start_date_str:
        start_date = parse_date_string(start_date_str)

    if end_date_str:
        end_date = parse_date_string(end_date_str)

    await main(start_date=start_date, end_date=end_date)

async def main_clean():
        graphiti_settings = GraphitiSettings(
            openai_api_key=settings.qwen_api_key,
            openai_base_url=settings.qwen_base_url,
            model_name=settings.qwen_model_name,
            embedding_model=settings.qwen_embedding_model,
            embedding_dim=settings.qwen_embedding_dim,
            neo4j_uri=settings.neo4j_uri,
            neo4j_user=settings.neo4j_user,
            neo4j_password=settings.neo4j_password
        )
        async with get_graphiti(graphiti_settings) as graphiti:
            await graphiti.clear_data()
            logger.info("Data cleared")
            
if __name__ == "__main__":
    import argparse

    # 设置日志级别
    logging.basicConfig(level=logging.INFO)

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='工作日报处理Agent')
    parser.add_argument('-a', '--action',  choices=["daily", "clean"], default="daily", help='操作类型 [daily, clean], 日报， 清理')
    parser.add_argument('-s', '--start-date', type=str, default=(datetime.now() - timedelta(days=1)).strftime("%Y%m%d"), help='开始日期，格式：YYYYMMDD')
    parser.add_argument('-e', '--end-date', type=str, default=datetime.now().strftime("%Y%m%d"), help='结束日期，格式：YYYYMMDD')

    args = parser.parse_args()

    if args.action == "daily":
        # 运行Agent
        asyncio.run(main_with_date_strings(
            start_date_str=args.start_date,
            end_date_str=args.end_date
        ))
    if args.action == "clean":
        # 清理数据
        asyncio.run(main_clean())
