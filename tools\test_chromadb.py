#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import os

os.environ["HTTP_PROXY"] = "http://**********:1080"
os.environ["HTTPS_PROXY"] = "http://**********:1080"

import chromadb
from chromadb.config import Settings

client = chromadb.HttpClient(
    host="**********",
    port=8000,
    settings=Settings(
        chroma_client_auth_provider="chromadb.auth.basic_authn.BasicAuthClientProvider",
        chroma_client_auth_credentials="admin:Smile5More#apper1",
        chroma_server_ssl_enabled=False,
        anonymized_telemetry=False
        ),
    tenant="banyunjuhe",
    database="embeddings"
)


# if everything is correctly configured the below should list all collections
print(client.list_collections())

client = chromadb.HttpClient(
    host="**********",
    port=8000,
    settings=Settings(
        chroma_client_auth_provider="chromadb.auth.basic_authn.BasicAuthClientProvider",
        chroma_client_auth_credentials="admin:Smile5More#apper1",
        chroma_server_ssl_enabled=False,
        anonymized_telemetry=False
        ),
    tenant="banyunjuhe",
    database="datasource"
)


# if everything is correctly configured the below should list all collections
print(client.list_collections())
