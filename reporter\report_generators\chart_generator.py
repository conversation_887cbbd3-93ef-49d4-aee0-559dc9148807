import os
import json
import numpy as np
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import matplotlib as mpl
from datetime import datetime
import matplotlib.dates as mdates
import matplotlib.patches as mpatches
from matplotlib.ticker import MaxNLocator

class ChartGenerator:
    def __init__(self, data_path="data"):
        """
        初始化图表生成器
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.chart_path = f"{data_path}/charts"
        os.makedirs(self.chart_path, exist_ok=True)
        
        # # 设置中文字体支持
        # plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        # plt.rcParams['axes.unicode_minus'] = False
    
        sns.set_style("whitegrid")

        # 指定默认字体  
        mpl.rcParams['font.family']='sans-serif'  
        mpl.rcParams['font.sans-serif'] = ['SimHei']   
        # 解决负号'-'显示为方块的问题  
        mpl.rcParams['axes.unicode_minus'] = False 

    def _load_all_data(self):
        """
        加载所有相关数据
        
        Returns:
            包含所有数据的字典
        """
        data = {}
        
        # 定义需要加载的数据文件
        data_files = [
            "ipct_day_data",
            "ipct_gen_image", 
            "heli_income_sum",
            "heli_day_data",
            "heli_hebao_active",
            "heli_hebao_policy",
            "conversion_day_data"
        ]
        
        # 加载所有数据文件
        for file_name in data_files:
            try:
                file_path = f"{self.data_path}/{file_name}.json"
                if os.path.exists(file_path):
                    with open(file_path, "r", encoding="utf-8") as f:
                        data[file_name] = json.load(f)
                else:
                    data[file_name] = []
            except Exception as e:
                print(f"Warning: Failed to load {file_name}: {e}")
                data[file_name] = []
                
        return data
    
    def _process_data(self, raw_data):
        """
        处理原始数据，转换为图表所需格式
        
        Args:
            raw_data: 从JSON文件加载的原始数据
            
        Returns:
            处理后的数据字典
        """
        processed_data = {}
        
        # 处理爱拼长图DAU数据
        if "ipct_day_data" in raw_data and raw_data["ipct_day_data"]:
            df_ipct_day = pd.DataFrame(raw_data["ipct_day_data"]) 
            df_ipct_day.rename(columns={"data_date": "日期", "uv": "活跃用户", "pay_user": "付费用户"}, inplace=True)
            df_ipct_day["日期"] = pd.to_datetime(df_ipct_day["日期"])
            df_ipct_day.sort_values(by="日期", ascending=True, inplace=True)  # 图表需要升序
            processed_data["aiping_dau"] = df_ipct_day
            
        # 处理爱拼长图生图数据
        if "ipct_gen_image" in raw_data and raw_data["ipct_gen_image"]:
            df_ipct_gen = pd.DataFrame(raw_data["ipct_gen_image"])
            df_ipct_gen.rename(columns={"data_date": "日期", "channel": "渠道", "num": "生图次数", "uv": "生图用户"}, inplace=True)
            df_ipct_gen["日期"] = pd.to_datetime(df_ipct_gen["日期"])
            df_ipct_gen.sort_values(by="日期", ascending=True, inplace=True)  # 图表需要升序
            processed_data["aiping_gen_image_data"] = df_ipct_gen
            
        # 处理赫鲤收入数据（按天展示）
        if "heli_income_sum" in raw_data and raw_data["heli_income_sum"]:
            df_heli_income = pd.DataFrame(raw_data["heli_income_sum"])
            df_heli_income.rename(columns={"data_date": "日期", "income": "收入"}, inplace=True)
            df_heli_income["日期"] = pd.to_datetime(df_heli_income["日期"])
            df_heli_income.sort_values(by="日期", ascending=True, inplace=True)  # 图表需要升序
            processed_data["heli_income_by_day"] = df_heli_income
            
        # 处理赫鲤日数据
        if "heli_day_data" in raw_data and raw_data["heli_day_data"]:
            df_heli_day = pd.DataFrame(raw_data["heli_day_data"])
            # 按日期和渠道分组聚合数据
            df_channel_metrics = df_heli_day.groupby(['data_date', 'qudao']).agg({
                'boot_uv': 'sum',
                'login_suc_uv': 'sum',
                'pay_suc_uv': 'sum'
            }).reset_index()
            df_channel_metrics.rename(columns={"data_date": "日期", "qudao": "渠道", "boot_uv": "启动用户", "login_suc_uv": "登录成功用户", "pay_suc_uv": "付费用户"}, inplace=True)
            df_channel_metrics["日期"] = pd.to_datetime(df_channel_metrics["日期"])
            df_channel_metrics.sort_values(by="日期", ascending=True, inplace=True)  # 图表需要升序
            processed_data["heli_channel_metrics"] = df_channel_metrics
            
        # 处理转化漏斗数据
        if "conversion_day_data" in raw_data and raw_data["conversion_day_data"]:
            df_data = pd.DataFrame(raw_data["conversion_day_data"])
            # 重命名列以匹配中文显示
            df_data.rename(columns={
                "data_date": "日期",
                "boot_uv": "启动用户",
                "privacy_uv": "隐私页用户",
                "hebao_uv": "荷包页用户",
                "login_uv": "登录页用户",
                "login_suc_uv": "登录成功用户",
                "pay_uv": "付费页用户",
                "confirmpay_uv": "确认付费用户",
                "pay_suc_uv": "付费成功用户"
            }, inplace=True)
            df_data["日期"] = pd.to_datetime(df_data["日期"])
            processed_data["conversion_data"] = df_data
            
        return processed_data
    
    def generate_aiping_dau_chart(self, df_data):
        """
        生成爱拼长图DAU趋势图
        
        Args:
            df_data: 爱拼长图DAU数据
            
        Returns:
            图表文件路径
        """
        if df_data.empty:
            return None
            
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))

        # 绘制活跃用户和付费用户趋势
        ax.plot(df_data["日期"], df_data["活跃用户"], marker='o', label='活跃用户', linewidth=2)
        ax.plot(df_data["日期"], df_data["付费用户"], marker='s', label='付费用户', linewidth=2)

        # 添加数值标签
        for i, row in df_data.iterrows():
            ax.annotate(f'{row["活跃用户"]}', 
                        (row['日期'], row['活跃用户']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)
            ax.annotate(f'{row["付费用户"]}', 
                        (row['日期'], row['付费用户']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)

        # 设置图表标题和标签
        ax.set_title("爱拼长图近七天用户活跃趋势", fontsize=16, pad=20)
        ax.set_xlabel("日期", fontsize=12)
        ax.set_ylabel("用户数", fontsize=12)
        ax.legend()
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))
        plt.xticks(rotation=45)
        plt.tight_layout()
            
        # 保存图表
        chart_file = f"{self.chart_path}/aiping_dau_trend.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_file
    
    def generate_aiping_gen_image_chart(self, df_data):
        """
        生成爱拼长图生图次数趋势图
        
        Args:
            df_data: 爱拼长图生图数据
            
        Returns:
            图表文件路径
        """
        if df_data.empty:
            return None
        
        # 按日期汇总
        daily_summary = df_data.groupby('日期').agg({
            '生图次数': 'sum',
            '生图用户': 'sum'
        }).reset_index().sort_values('日期')

        # 按渠道汇总
        channel_summary = df_data.groupby('渠道').agg({
            '生图次数': 'sum',
            '生图用户': 'sum'
        }).reset_index().sort_values('生图次数', ascending=False)

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # 1. 每日生图趋势
        ax1.plot(daily_summary['日期'], daily_summary['生图次数'], 
                marker='o', linewidth=3, markersize=8, color='#1f77b4', label='生图次数')
        ax1.plot(daily_summary['日期'], daily_summary['生图用户'], 
                marker='s', linewidth=2, markersize=6, color='#2ca02c', label='生图用户')

        ax1.set_title('每日生图趋势', fontsize=14, fontweight='bold')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('数量')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)

        # 设置 Y 轴范围从 0 开始
        ax1.set_ylim(0, max(daily_summary['生图次数'].max(), daily_summary['生图用户'].max()) * 1.1)
        # 设置 Y 轴刻度为整数
        ax1.yaxis.set_major_locator(MaxNLocator(integer=True))

        # 添加数值标签
        for i, row in daily_summary.iterrows():
            ax1.annotate(f'{int(row["生图次数"])}', 
                        (row['日期'], row['生图次数']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)
        for i, row in daily_summary.iterrows():
            ax1.annotate(f'{int(row["生图用户"])}', 
                        (row['日期'], row['生图用户']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)
            

        # 8. 各渠道生图次数占比的饼图
        ax2.pie(channel_summary['生图次数'], labels=channel_summary['渠道'], autopct='%1.1f%%', 
                colors=plt.cm.Set3.colors, startangle=90)
        ax2.set_title('各渠道生图次数占比', fontsize=14, fontweight='bold')

        plt.tight_layout()
            
        # 保存图表
        chart_file = f"{self.chart_path}/aiping_gen_image_trend.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_file
    
    def generate_heli_income_chart(self, df_data):
        """
        生成赫鲤收入趋势图
        
        Args:
            df_data: 赫鲤收入数据
            
        Returns:
            图表文件路径
        """
        if df_data.empty:
            return None
            
        plt.figure(figsize=(12, 6))

        # 绘制收入趋势
        plt.plot(df_data["日期"], df_data["收入"], marker='o', color='green', linewidth=2)

        # 添加每个数据点的数值标签
        for i, row in df_data.iterrows():
            plt.annotate(f'{row["收入"]}',
                        (row['日期'], row['收入']),
                        textcoords="offset points", xytext=(0, 10), ha='center', fontsize=8)

        plt.title("赫鲤显微镜近七天收入趋势", fontsize=16, pad=20)
        plt.xlabel("日期", fontsize=12)
        plt.ylabel("收入", fontsize=12)
        plt.xticks(rotation=45)

        # 设置日期格式
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=1))
        plt.tight_layout()
        
        # 保存图表
        chart_file = f"{self.chart_path}/heli_income_trend.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_file
    
    def generate_heli_channel_metrics_chart(self, df):
        """
        生成赫鲤渠道指标对比图
        
        Args:
            df_data: 赫鲤渠道指标数据
            
        Returns:
            图表文件路径列表
        """
        if df.empty:
            return []

        # 数据清洗
        df['启动用户'] = pd.to_numeric(df['启动用户'], errors='coerce').fillna(0)
        df['登录成功用户'] = pd.to_numeric(df['登录成功用户'], errors='coerce').fillna(0)
        df['付费用户'] = pd.to_numeric(df['付费用户'], errors='coerce').fillna(0)
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 按渠道汇总数据
        channel_summary = df.groupby('渠道').agg({
            '启动用户': 'sum',
            '登录成功用户': 'sum',
            '付费用户': 'sum'
        }).reset_index()

        # 按启动用户数量降序排序
        channel_summary = channel_summary.sort_values(by='启动用户', ascending=False)

        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 8))

        # 主要渠道用户数对比
        bar_width = 0.25
        index = range(len(channel_summary))

        # 启动用户
        ax.bar(index, channel_summary['启动用户'], bar_width, color='#1f77b4', label='启动用户')

        # 登录成功用户
        ax.bar([i + bar_width for i in index], channel_summary['登录成功用户'], bar_width, color='#2ca02c', label='登录成功用户')

        # 付费用户
        ax.bar([i + 2 * bar_width for i in index], channel_summary['付费用户'], bar_width, color='#ff7f0e', label='付费用户')

        # 设置图表标题和标签
        ax.set_title('主要渠道用户数对比', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('渠道', fontsize=12)
        ax.set_ylabel('用户数量', fontsize=12)
        ax.set_xticks([i + bar_width for i in index])
        ax.set_xticklabels(channel_summary['渠道'], rotation=45, ha='right')
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for i, value in enumerate(channel_summary['启动用户']):
            ax.text(i, value + 5, str(value), ha='center', va='bottom', rotation=45, fontsize=8, color='#1f77b4')

        for i, value in enumerate(channel_summary['登录成功用户']):
            ax.text(i + bar_width, value + 5, str(value), ha='center', va='bottom', rotation=45, fontsize=8, color='#2ca02c')

        for i, value in enumerate(channel_summary['付费用户']):
            ax.text(i + 2 * bar_width, value + 5, str(value), ha='center', va='bottom', rotation=45, fontsize=8, color='#ff7f0e')

        plt.tight_layout()

        # 保存图表
        chart_file = f"{self.chart_path}/heli_channel_combined.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_file
    
    def generate_heli_channel_metrics_combined_chart(self, df):
        """
        生成赫鲤渠道指标对比图（所有天数合并显示，横向柱状图）
        
        Args:
            df_data: 赫鲤渠道指标数据
            
        Returns:
            图表文件路径
        """
        if df.empty:
            return None
            
        # 数据清洗
        df['启动用户'] = pd.to_numeric(df['启动用户'], errors='coerce').fillna(0)
        df['登录成功用户'] = pd.to_numeric(df['登录成功用户'], errors='coerce').fillna(0)  
        df['付费用户'] = pd.to_numeric(df['付费用户'], errors='coerce').fillna(0)
        df['日期'] = pd.to_datetime(df['日期'])

        # 按日期汇总数据
        daily_summary = df.groupby('日期').agg({
            '启动用户': 'sum',
            '登录成功用户': 'sum', 
            '付费用户': 'sum'
        }).reset_index()

        # 排序日期
        daily_summary = daily_summary.sort_values('日期')

        # 计算转化率，重新计算了，不再是原始表格上的的数据了
        daily_summary['登录率'] = (daily_summary['登录成功用户'] / daily_summary['启动用户'] * 100).round(2)
        daily_summary['付费率'] = (daily_summary['付费用户'] / daily_summary['启动用户'] * 100).round(2)

        print("每日汇总数据:")
        print(daily_summary)
        print("\n" + "="*50 + "\n")

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 图1：每日用户数量趋势
        ax1.plot(daily_summary['日期'], daily_summary['启动用户'], 
                marker='o', linewidth=3, markersize=8, color='#1f77b4', label='启动用户')
        ax1.plot(daily_summary['日期'], daily_summary['登录成功用户'], 
                marker='s', linewidth=2, markersize=6, color='#2ca02c', label='登录成功用户')
        ax1.plot(daily_summary['日期'], daily_summary['付费用户'], 
                marker='^', linewidth=2, markersize=6, color='#ff7f0e', label='付费用户')

        ax1.set_title('每日用户数量趋势', fontsize=16, fontweight='bold', pad=20)
        ax1.set_xlabel('日期', fontsize=12)
        ax1.set_ylabel('用户数量', fontsize=12)
        ax1.legend(fontsize=10)
        ax1.grid(True, alpha=0.3)

        # 设置日期格式
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.DayLocator(interval=1))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

        # 添加数值标签
        for i, row in daily_summary.iterrows():
            ax1.annotate(f'{int(row["启动用户"])}', 
                        (row['日期'], row['启动用户']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)

        # 图2：每日转化率趋势
        ax2.plot(daily_summary['日期'], daily_summary['登录率'], 
                marker='o', linewidth=2, markersize=6, color='#1f77b4', label='登录率 (%)')
        ax2.plot(daily_summary['日期'], daily_summary['付费率'], 
                marker='^', linewidth=2, markersize=6, color='#d62728', label='付费率 (%)')

        ax2.set_title('每日转化率趋势', fontsize=16, fontweight='bold', pad=20)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.set_ylabel('转化率 (%)', fontsize=12)
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)

        # 设置日期格式
        ax2.xaxis.set_major_locator(mdates.DayLocator(interval=1))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

        # 添加数值标签
        for i, row in daily_summary.iterrows():
            ax1.annotate(f'{int(row["启动用户"])}', 
                        (row['日期'], row['启动用户']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)
            ax1.annotate(f'{int(row["登录成功用户"])}', 
                        (row['日期'], row['登录成功用户']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)
            ax1.annotate(f'{int(row["付费用户"])}', 
                        (row['日期'], row['付费用户']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)

        # 添加数值标签
        for i, row in daily_summary.iterrows():
            ax2.annotate(f'{float(row["付费率"])}%', 
                        (row['日期'], row['付费率']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)
            ax2.annotate(f'{float(row["登录率"])}%', 
                        (row['日期'], row['登录率']), 
                        textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)
            
            
        plt.tight_layout()
        # plt.show()
        
        # 保存图表
        chart_file = f"{self.chart_path}/heli_channel_metrics_combined.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_file
    
    def generate_conversion_funnel_chart(self, df_data):
        """
        生成转化漏斗图
        
        Args:
            df_data: 转化漏斗数据
            
        Returns:
            图表文件路径
        """
        if df_data.empty:
            return None
            
        funnel_data = df_data.iloc[0]
        data_date = funnel_data["日期"].strftime("%Y-%m-%d")

        # 定义漏斗环节的顺序
        stages_order = [
            '启动用户', 
            '隐私页用户', 
            '荷包页用户', 
            '登录页用户', 
            '登录成功用户', 
            '付费页用户', 
            '确认付费用户', 
            '付费成功用户'
        ]

        # 提取各环节用户数
        funnel_stages = [(stage, funnel_data[stage]) for stage in stages_order]

        # 创建转化率数据
        funnel_df = pd.DataFrame(funnel_stages, columns=['环节', '用户数'])



        funnel_df = pd.DataFrame(funnel_stages, columns=['环节', '用户数'])
        funnel_df['总转化率(%)'] = (funnel_df['用户数'] / funnel_data['启动用户'] * 100).round(2)
        funnel_df['步骤转化率(%)'] = funnel_df['用户数'].pct_change().fillna(0) * 100
        funnel_df.loc[0, '步骤转化率(%)'] = 100.0

        for i in range(1, len(funnel_df)):
            funnel_df.loc[i, '步骤转化率(%)'] = (funnel_df.loc[i, '用户数'] / funnel_df.loc[i-1, '用户数'] * 100).round(2)

        # 创建漏斗图
        fig, ax = plt.subplots(figsize=(10, 8))

        # 漏斗图数据
        stages = [stage[0] for stage in funnel_stages]
        values = [stage[1] for stage in funnel_stages]
        colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#0088fe', '#00c49f', '#ffbb28', '#ff8042']

        # 绘制漏斗
        y_positions = np.arange(len(stages))[::-1]  # 从上到下排列
        max_width = max(values)

        for i, (stage, value) in enumerate(funnel_stages):
            # 计算当前梯形的宽度
            width = value / max_width * 0.8
            left = (1 - width) / 2
            
            # 计算上一个梯形的宽度（用于当前梯形的上边缘）
            if i > 0:
                top_width = (values[i-1] / max_width * 0.8)
                top_left = (1 - top_width) / 2
            else:
                top_width = width
                top_left = left
            
            # 绘制梯形 - 关键修改：确保相邻梯形边缘完全对齐
            rect = mpatches.Polygon([
                (left, y_positions[i] - 0.5),       # 左下角 - 调整为-0.5
                (1 - left, y_positions[i] - 0.5),   # 右下角 - 调整为-0.5
                (1 - top_left, y_positions[i] + 0.5), # 右上角 - 调整为+0.5
                (top_left, y_positions[i] + 0.5)    # 左上角 - 调整为+0.5
            ], closed=True, facecolor=colors[i], alpha=0.8, edgecolor='none', linewidth=0)
            ax.add_patch(rect)

            # 添加阶段名称到左边
            ax.text(0.00, y_positions[i], stage, ha='left', va='center', fontsize=15, fontweight='bold', color=colors[i])
            
            # 添加用户数和转化率到梯形内部
            ax.text(0.5, y_positions[i], f'{value:,}人\n({funnel_df.iloc[i]["总转化率(%)"]}%)', 
                    ha='center', va='center', fontsize=15, fontweight='bold', color='white',
                    bbox=dict(facecolor='black', alpha=0.2, edgecolor='none', boxstyle='round'))
            

        # 设置坐标轴范围
        ax.set_xlim(0, 1)
        ax.set_ylim(-0.5, len(stages) - 0.5)
        ax.set_yticks([])
        ax.set_xticks([])
        ax.set_title(f'用户行为漏斗图', fontsize=20, fontweight='bold', pad=15)

        # 移除边框
        for spine in ax.spines.values():
            spine.set_visible(False)

        plt.tight_layout()

        # 保存图表
        chart_file = f"{self.chart_path}/conversion_funnel.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_file
    
    def generate_all_charts(self):
        """
        生成所有图表
        
        Returns:
            生成的图表文件路径列表
        """
        print("Generating charts...")
        
        # 加载所有数据
        raw_data = self._load_all_data()
        
        # 处理数据
        processed_data = self._process_data(raw_data)
        
        chart_files = []
        
        # 生成爱拼长图DAU趋势图
        if "aiping_dau" in processed_data:
            chart_file = self.generate_aiping_dau_chart(processed_data["aiping_dau"])
            if chart_file:
                chart_files.append(chart_file)
                print(f"Generated chart: {chart_file}")
        
        # 生成爱拼长图生图次数趋势图
        if "aiping_gen_image_data" in processed_data:
            chart_file = self.generate_aiping_gen_image_chart(processed_data["aiping_gen_image_data"])
            if chart_file:
                chart_files.append(chart_file)
                print(f"Generated chart: {chart_file}")
        
        # 生成赫鲤收入趋势图
        if "heli_income_by_day" in processed_data:
            chart_file = self.generate_heli_income_chart(processed_data["heli_income_by_day"])
            if chart_file:
                chart_files.append(chart_file)
                print(f"Generated chart: {chart_file}")
        
        # 生成赫鲤渠道指标对比图
        if "heli_channel_metrics" in processed_data:
            channel_chart_file = self.generate_heli_channel_metrics_chart(processed_data["heli_channel_metrics"])
            if channel_chart_file:
                chart_files.append(channel_chart_file)
                print(f"Generated chart: {channel_chart_file}")
            
            # 生成赫鲤渠道指标对比图（所有天数合并显示）
            combined_chart_file = self.generate_heli_channel_metrics_combined_chart(processed_data["heli_channel_metrics"])
            if combined_chart_file:
                chart_files.append(combined_chart_file)
                print(f"Generated chart: {combined_chart_file}")
        
        # 生成转化漏斗图
        if "conversion_data" in processed_data:
            conversion_chart_file = self.generate_conversion_funnel_chart(processed_data["conversion_data"])
            if conversion_chart_file:
                chart_files.append(conversion_chart_file)
                print(f"Generated chart: {conversion_chart_file}")
        
        print(f"Generated {len(chart_files)} charts.")
        return chart_files
