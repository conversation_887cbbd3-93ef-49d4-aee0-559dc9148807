from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
import time 
import os 

os.environ["LANGSMITH_TRACING"]="true"
os.environ["LANGCHAIN_ENDPOINT"]="https://api.smith.langchain.com"
os.environ["LANGSMITH_API_KEY"]="***************************************************"
os.environ["LANGSMITH_PROJECT"]="api-llamaindex"

system_template = """
你是一个智能助手，根据用户的问题，分析其意图并匹配最合适的工具来解决问题。
今天的日期是{data_date}。

你拥有以下工具：
{tools}

你拥有以下记忆:
{user_info}

对你的要求：
（1）分析用户输入
仔细阅读用户的问题，识别意图。
判断问题属于以下哪类：使用工具或者直接回答

（2）匹配工具
依据问题匹配生成合适的查询或者明确的工具调用。

（3）遵守规则
避免直接假设用户需求，除非意图非常明确。
如果问题涉及敏感话题（如“谁该死”），回复：“我无法做出这种判断。”
"""

user_template = """
{messages}
{question}
"""
prompt = ChatPromptTemplate.from_messages([("system", system_template), ("human", user_template)])
model = ChatOpenAI(
    model="gpt-4o-mini",
    temperature=0.7,
    max_tokens=16384,
    api_key="********************************************************************************************************************************************************************"
)

agent = prompt | model 
data_date = time.strftime("%Y年%m月%d日", time.localtime())
msg = agent.invoke({"data_date": data_date, "tools":[], "messages":[], "user_info":"我是属鼠的", "question": "你好,我是属什么的？"})

print(msg)
