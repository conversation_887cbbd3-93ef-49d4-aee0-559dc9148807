# report_generators/ai_generator.py
import json
import os
from typing import Dict, Any
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain_core.messages import HumanMessage, SystemMessage

class AIReportGenerator:
    def __init__(self, config, data_path):
        """
        初始化AI报告生成器
        
        Args:
            config: 包含Qwen API配置的配置对象
        """
        self.config = config
        self.data_path = data_path 
        # 初始化Qwen模型
        self.llm = ChatOpenAI(
            model=config.qwen_model_name,
            temperature=0.7,
            max_tokens=2048,
            api_key=config.qwen_api_key,
            openai_api_base=config.qwen_base_url
        )
        
    def _load_all_data(self) -> str:
        """
        加载运营数据
        
        Returns:
            运营数据内容
        """
        data = ""
        with open(self.data_path, "r", encoding="utf-8") as f:
            data = f.read()
        return data
   
    def summary(self) -> str:
        """
        调用Qwen-plus来解读报表，提供业务运行情况、是否异常以及运营建议
        
        Returns:
            AI生成的分析报告
        """
        # 加载所有数据
        data = self._load_all_data()
        
        # 构建提示词
        prompt_template = """
你是一个专业的业务分析师，请根据以下数据进行分析，提供业务运行情况、是否异常以及运营建议。

以下是两款产品的档案和愿景。

名称,爱拼长图,,
实际需求,"真的能学会使用AI生图，而不是一键换脸等傻瓜式的操作。
有灵感但无图可用。",,
当前方案,"通过部署自学使用官方工具。
跨平台多个ai工具去实现创作。",,
爱拼长图定位,AI生图新标配,2.2.51,
,引领新一代AI视觉创作神器,2.2.49,
,对比市面上限制次数的app：万能AI生图不限次,2.2.49,
,目标二次元插画风：画出你的名场面,2.2.45,
,免费AI助力拼图修图,2.2.44,
,专业设计师都在用的创意AI图片工具,2.2.40,
特点,有海量创意日日上新，模版风格周周上新,,
,有可调整的参数，AI没那么简单,,
,一键生图，制作同款，再次生图满足每个环节的尝试路径,,
产品主功能,"产品功能：
拼图、生图、创意合一
自由拼接长图
AI一键生图
AI图片创作
AI视频创作
AI特效创意","六大优势，点燃AI视觉创作热潮：
大模型专研团队，专注AI视觉创新
多模型集成，创作体验全面升级
创意拼图新方式，打破传统表达边界
丰富参数设置，满足多元创作需求
操作极简，轻松驾驭专业级效果
适用于各类视觉内容创作场景，覆盖广泛用户
","超自由  多模型赋能，灵感无限释放
真创意  参数丰富，玩法多元，想象力全开
极简单  一键出片，轻松上手，人人都是视觉艺术家
巨精彩  二次元、书粉、内容创作者都能玩出花样
"
凭什么是我们做出来,大厂会侧重于更通用的需求和场景。,,

应用名称,赫鲤显微镜
名字解析,“赫鲤”谐音“贺礼”，给孩子的贺礼；“显微镜”告诉用户在app内能得到什么，构造想象
口号,超高清 真动手 极有趣 巨划算
定位,仿真在线生物实验室
功能,看显微镜
,能操作，调整参数
,有ai答疑
,能生成、分享实验报告
六大优势,7年开发团队沉淀
,生物硕博团队支持
,实验教学覆盖全学段
,成长系统让孩子深度学习
,专为中国学生研发
,专业级国家标准器材
付费策略数据,高充vs低充
情绪单元，要很完整。
产品卖点,备注,
孩子成长焦虑,,
沉浸式体验,,
知识科普+显微镜下图片,
随时随地学知识,
低成本体验高级显微镜,成本：买了显微镜在家吃灰的成本考虑,
教育平权,地域，三四线及偏远地区的痛点,
时间平权,地域，发达城市时间痛点,
中年人共情教育经历,人群，中年人回忆自己的成长教育过程，对自己当时没有这么好的条件的情绪,

数据如下：
{data}

请按照以下结构进行分析：
1. 总体业务运行情况概述
2. 运营建议
   - 针对发现的问题提出具体建议
   - 优化方案和改进措施

请用中文回复，内容要具体、实用，避免空泛、冗长的表述。
        """
        
        # 创建提示模板
        prompt = PromptTemplate.from_template(prompt_template)
        formatted_prompt = prompt.format(data=data)
        
        # 调用Qwen-plus模型
        try:
            response = self.llm.invoke([
                SystemMessage(content="你是一个专业的业务分析师，擅长数据分析和业务洞察。"),
                HumanMessage(content=formatted_prompt)
            ])
            return response.content
        except Exception as e:
            error_msg = f"AI分析失败: {str(e)}"
            print(error_msg)
            return error_msg
