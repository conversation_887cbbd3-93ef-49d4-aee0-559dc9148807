import asyncio

# Example of a synchronous function
def sync_function(text:str):
    print(f"Running a synchronous function for {text}")
    # Simulate a blocking operation
    import time
    time.sleep(2)
    return "Completed"

# Asynchronous wrapper for the synchronous function
async def async_wrapper(func, *args):
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(None, func, *args)
    return result

# Main function to run the async wrapper
async def main():
    data = await async_wrapper(sync_function, "miss you!")
    print(data)
    
# Run the main function
if __name__ == "__main__":
    asyncio.run(main())
