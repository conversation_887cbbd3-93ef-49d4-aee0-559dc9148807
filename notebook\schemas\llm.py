
# Define request body model
from typing import List, Optional
from pydantic import BaseModel, Field


class ContextItem(BaseModel):
    content: str
    role: str

class Config(BaseModel):
    maxTokens: int = 16384
    model: str = "gpt-4o-latest"
    sendMemory: bool = True
    temperature: float = 0.6

class LLMRequest(BaseModel):
    chatHistory: Optional[List[ContextItem]] = None
    config: Config
    context: Optional[List[ContextItem]] = None
    datasource: Optional[str] = None 
    embeddings: Optional[List[str]] = None
    message: Optional[str] = None
    image:Optional[List[str]] = None
    userId: str
    assistantId: str
    search:Optional[bool] = False
    tenantId: str = Field(
        "banyunjuhe", # Default value
        pattern=r"^[a-zA-Z0-9_-]+$", # Regex pattern for alphanumeric, dashes, underscores
        description="Tenant ID can only contain alphanumeric characters, dashes, or underscores."
    )

