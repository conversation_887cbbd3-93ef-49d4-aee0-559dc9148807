# utils/wecom_robot.py
import requests
import json
import os
import base64
import hashlib
import time 
from datetime import datetime

class WeComRobot:
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url

    def _get_webhook_key(self) -> str | None:
        """从 webhook url 中提取 key"""
        if "?key=" in self.webhook_url:
            return self.webhook_url.split("?key=")[1]
        print("Error: Could not extract webhook key from URL.")
        return None

    def _upload_file(self, file_path: str, file_type: str) -> str | None:
        """
        上传文件到企业微信，并返回 media_id。
        :param file_path: 文件路径
        :param file_type: 文件类型，可以是 'voice', 'file'
        :return: media_id or None
        """
        key = self._get_webhook_key()
        if not key:
            return None
        
        # 根据文档，上传接口的URL是固定的
        upload_url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key={key}&type={file_type}"
        
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return None

        try:
            with open(file_path, 'rb') as f:
                # 'media' 是API要求的字段名
                files = {'media': (os.path.basename(file_path), f, 'application/octet-stream')}
                response = requests.post(upload_url, files=files)
                response.raise_for_status()
                result = response.json()
                if result.get('errcode') == 0:
                    print(f"File '{file_path}' uploaded successfully. Media ID: {result.get('media_id')}")
                    return result.get('media_id')
                else:
                    print(f"Failed to upload file: {result.get('errmsg', 'Unknown error')}")
                    return None
        except requests.exceptions.RequestException as e:
            print(f"HTTP request failed during file upload: {e}")
            return None
        except Exception as e:
            print(f"An unexpected error occurred during file upload: {e}")
            return None

    def send_text_message(self, content: str, mentioned_list: list = None, mentioned_mobile_list: list = None):
        """
        发送文本消息。
        """
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        if mentioned_list:
            data["text"]["mentioned_list"] = mentioned_list
        if mentioned_mobile_list:
            data["text"]["mentioned_mobile_list"] = mentioned_mobile_list
        return self._send_message(data)

    def send_markdown_message(self, content: str):
        """
        发送Markdown消息。
        """
        content_len = len(content.encode('utf-8'))
        print(f"Markdown content length: {content_len}")
        if content_len > 4096:
            # 如果内容超过4096字符，进行智能分割，确保表格不被截断
            parts = self._split_markdown_content(content)
            for part in parts:
                data = {
                    "msgtype": "markdown_v2",
                    "markdown_v2": {
                        "content": part
                    }
                }
                self._send_message(data)
        else:            
            data = {
                "msgtype": "markdown_v2",
                "markdown_v2": {
                    "content": content
                }
            }
            return self._send_message(data)

    def _split_markdown_content(self, content: str) -> list:
        """
        智能分割Markdown内容，确保表格不被截断。
        :param content: 原始Markdown内容
        :return: 分割后的内容列表
        """
        parts = []
        lines = content.split('\n')
        current_part = ""
        in_table = False
        table_header = ""
        table_subheader = ""
        for line in lines:
            # 检查是否进入或退出表格
            if line.strip().startswith('|') and line.strip().endswith('|') and not in_table:
                in_table = True
                table_header = line
            elif in_table and line.strip().startswith('|:-') and line.strip().endswith('--|'):
                table_subheader = line
            elif in_table and not line.strip().startswith('|') and not line.strip() == '':
                # 退出表格（遇到非表格行且非空行）
                in_table = False
            
            # 检查添加当前行后是否会超过限制
            if len(current_part.encode('utf-8')) + len(line.encode('utf-8')) + 1 > 3800 and not in_table:
                # 如果不在表格中，可以安全分割
                parts.append(current_part)
                current_part = line + '\n'
            elif len(current_part.encode('utf-8')) + len(line.encode('utf-8')) + 1 > 3800 and in_table:
                # 如果在表格中且超过限制，需要特殊处理
                # 输出当前表格
                # 新表格添加表头

                parts.append(current_part)
                current_part = table_header + '\n' + table_subheader + '\n' + line + '\n'
            else:
                current_part += line + '\n'
        
        # 添加最后一部分
        if current_part:
            parts.append(current_part)
        
        # 确保每个部分不超过1500字符
        final_parts = []
        for part in parts:
            if len(part.encode('utf-8')) <= 4096:
                final_parts.append(part)
            else:
                # 如果仍有部分超过1500字符，强制分割
                for i in range(0, len(part.encode('utf-8')), 4096):
                    final_parts.append(part[i:i+4096])
        
        return final_parts

    def send_markdown_file(self, data_file: str):
        """
        发送Markdown文件。
        """
        if not os.path.exists(data_file):
            print(f"File not found: {data_file}")
            return False
            
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 直接发送整个文件内容，不分割
            return self.send_markdown_message(content)
        except Exception as e:
            print(f"Failed to read or send markdown file: {e}")
            return False

        
    def send_image_message(self, image_path: str) -> bool:
        """
        发送图片消息。
        图片（base64编码前）最大不能超过2M，支持JPG,PNG格式。
        """
        if not os.path.exists(image_path):
            print(f"Image file not found: {image_path}")
            return False
        
        if os.path.getsize(image_path) > 2 * 1024 * 1024:
            print("Image file size exceeds 2MB limit.")
            return False

        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
                base64_data = base64.b64encode(image_data).decode('utf-8')
                md5_hash = hashlib.md5(image_data).hexdigest()

            data = {
                "msgtype": "image",
                "image": {
                    "base64": base64_data,
                    "md5": md5_hash
                }
            }
            return self._send_message(data)
        except Exception as e:
            print(f"Failed to process image file: {e}")
            return False

    def send_voice_message(self, voice_path: str) -> bool:
        """
        发送语音消息。
        语音文件最大不能超过2M，播放长度不超过60s，仅支持AMR格式。
        """
        if not os.path.exists(voice_path):
            print(f"Voice file not found: {voice_path}")
            return False

        if os.path.getsize(voice_path) > 2 * 1024 * 1024:
            print("Voice file size exceeds 2MB limit.")
            return False
            
        media_id = self._upload_file(voice_path, 'voice')
        if media_id:
            data = {
                "msgtype": "voice",
                "voice": {
                    "media_id": media_id
                }
            }
            return self._send_message(data)
        return False

    def send_file_message(self, file_path: str) -> bool:
        """
        发送文件消息。
        文件大小不超过20M。
        """
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return False

        if os.path.getsize(file_path) > 20 * 1024 * 1024:
            print("File size exceeds 20MB limit.")
            return False
            
        media_id = self._upload_file(file_path, 'file')
        if media_id:
            data = {
                "msgtype": "file",
                "file": {
                    "media_id": media_id
                }
            }
            return self._send_message(data)
        return False

    def send_text_notice_card(self, main_title: dict, sub_title_text: str = None, 
                              emphasis_content: dict = None, quote_area: dict = None,
                              horizontal_content_list: list = None, jump_list: list = None,
                              card_action: dict = None, source: dict = None) -> bool:
        """
        发送文本通知模板卡片消息。
        :param main_title: 主标题, e.g., {"title": "标题", "desc": "描述"}
        :param sub_title_text: 副标题文本
        :param emphasis_content: 关键数据, e.g., {"title": "100", "desc": "数据"}
        :param quote_area: 引用区, e.g., {"title": "引用文字", "quote_text": "引用内容"}
        :param horizontal_content_list: 二级标题+文本列表, e.g., [{"keyname": "姓名", "value": "小明"}, ...]
        :param jump_list: 跳转链接列表, e.g., [{"type": 1, "url": "http://www.qq.com", "title": "企业微信"}]
        :param card_action: 卡片右上角按钮, e.g., {"type": 1, "url": "http://www.qq.com"}
        :param source: 卡片来源, e.g., {"icon_url": "http://xxx.png", "desc": "企业微信"}
        :return: 是否发送成功
        """
        data = {
            "msgtype": "template_card",
            "template_card": {
                "card_type": "text_notice"
            }
        }
        
        template_card = data["template_card"]
        
        if main_title:
            template_card["main_title"] = main_title
            
        if sub_title_text:
            template_card["sub_title_text"] = sub_title_text
            
        if emphasis_content:
            template_card["emphasis_content"] = emphasis_content
            
        if quote_area:
            template_card["quote_area"] = quote_area
            
        if horizontal_content_list:
            template_card["horizontal_content_list"] = horizontal_content_list
            
        if jump_list:
            template_card["jump_list"] = jump_list
            
        if card_action:
            template_card["card_action"] = card_action
            
        if source:
            template_card["source"] = source
            
        return self._send_message(data)

    def _send_message(self, data: dict) -> bool:
        """
        发送消息到企业微信机器人。
        :param data: 消息数据
        :return: 是否发送成功
        """

        try:
            response = requests.post(self.webhook_url, json=data)
            response.raise_for_status()
            result = response.json()
            if result.get('errcode') == 0:
                print("Message sent successfully.")
                return True
            else:
                print(f"Failed to send message: {result.get('errmsg', 'Unknown error')}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"HTTP request failed: {e}")
            return False
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return False
        time.sleep(2)
