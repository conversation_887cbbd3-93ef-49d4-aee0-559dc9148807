from typing import List, Dict, Any, Annotated, Optional, Literal
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain.retrievers.merger_retriever import MergerRetriever

import time
from typing import Literal, Annotated, Optional
from langchain_core.prompts import PromptTemplate, ChatPromptTemplate
from langchain_core.messages import BaseMessage
from langgraph.prebuilt import tools_condition, ToolNode
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage,ToolMessage
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel, Field
from langchain_core.tools.simple import Tool
from langgraph.store.base import BaseStore

from langchain_tavily import TavilySearch
from ..utils.image_helper import scale_image

import uuid
from html import escape
import threading

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.prompts import PromptTemplate
from langgraph.graph import StateGraph, MessagesState, START, END
from langchain.tools.retriever import create_retriever_tool
from notebook.libs.langchain_lib import LangChainService
from ..config import get_settings
from ..schemas.llm import Config, ContextItem
import logging 
import time 
import os 
import traceback
from langchain_mcp_adapters.client import MultiServerMCPClient
import json
from ..utils.content_helper import compute_md5
from anyio import BrokenResourceError
import re
from ..mpc.mcp_server import create_image

chat_logger = logging.getLogger("chat")
settings = get_settings()

def init_llm_model(config:Config):
    
    model = None
    if config.model in settings.openai_models.split(","):
        chat_logger.info(f"{time.time()},init openai model {config.model}") 
        is_reasoning = config.model.startswith(("o1", "o3", "o4"))  # 简单判断
        config.temperature = None
        model = ChatOpenAI(
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.maxTokens,
            api_key=settings.openai_api_key
        )
        
    if config.model in settings.qwen_models.split(","):
        chat_logger.info(f"{time.time()},init qwen model {config.model}")        
        model = ChatOpenAI(
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.maxTokens,
            api_key=settings.qwen_api_key,
            openai_api_base=settings.qwen_base_url
        )
        
    if config.model in settings.openrouter_models.split(","):
        chat_logger.info(f"{time.time()},init openrouter model {config.model}")        
        model = ChatOpenAI(
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.maxTokens,
            api_key=settings.openrouter_api_key,
            openai_api_base=settings.openrouter_base_url
        )
    if config.model in settings.google_models.split(","):
        chat_logger.info(f"{time.time()},init google model {config.model}")        
        model = ChatOpenAI(
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.maxTokens,
            api_key=settings.google_api_key,
            openai_api_base=settings.google_base_url
        )
            
    return model

def init_embeddings_model():
    model = OpenAIEmbeddings(
        openai_api_key=settings.openai_api_key,
        model=settings.embedding_model,
        dimensions=settings.embedding_dim
        )
    return model 
     
def init_prompt_template(message: str, image:List[str], context: List[ContextItem] | None, chat_history: List[ContextItem] | None) -> Dict[str, Any]:
    message = escape_markdown_special_chars(message)
    # Create the system prompt
    system_prompt_messages = []
    if context:
        for item in context:
            if item.role == "system":
                system_prompt_messages.append(
                      SystemMessage(content=item.content)
                )
    buffer = []
    if chat_history:
        for item in chat_history:
            if item.role == "user":
                buffer.append(HumanMessage(content=item.content))
            elif item.role == "assistant":
                buffer.append(AIMessage(content=item.content))
            else:
                chat_logger.info(f"{time.time()},Get message {item}, unknown role: {item['role']}")
                    
    if image != None and len(image) > 0:
        new_message = HumanMessage(
            content=[{"type": "image_url",
                "image_url": {"url": scale_image(img)},
            } for img in image] + [{"type": "text", "text": message}]
        )
    else:
        new_message = HumanMessage(content=message)

    return {"messages": [*system_prompt_messages, *buffer, new_message]}

class AgenticRagMessagesState(MessagesState):
    
    relevance_score: Annotated[Optional[str], "Relevance score of retrieved documents, 'yes' or 'no'"]
    rewrite_count: Annotated[int, "Number of times query has been rewritten"]
    content_router: Annotated[Optional[str], "content rouer, 'generate' or 'assistant'"]
    
class ToolConfig:
    def __init__(self, tools:List[Tool]):
        
        self.tools = tools
        self.tool_names = {tool.name for tool in tools}
        self.tool_routing_config = self._build_routing_config(tools)
        self.tool_descs = {tool.name: f"{tool.name}:{tool.description}" for tool in tools}
        
    def _build_routing_config(self, tools):
       
        routing_config = {}
        for tool in tools:
            
            tool_name = tool.name.lower()
            if "retrieve" in tool_name:
                routing_config[tool_name] = "grade_documents"
                chat_logger.info(f"{time.time()},Tool '{tool_name}' routed to 'grade_documents' (retrieval tool)")
            else:
                routing_config[tool_name] = "generate"
                chat_logger.info(f"{time.time()},Tool '{tool_name}' routed to 'generate' (non-retrieval tool)")
        
        if not routing_config:
            chat_logger.warning(f"{time.time()},No tools provided or routing config is empty")

        return routing_config

    def get_tools(self):
        return self.tools

    def get_tool_names(self):
        return self.tool_names

    def get_tool_routing_config(self):
        return self.tool_routing_config

class DocumentRelevanceScore(BaseModel):
    binary_score: str = Field(description="Relevance score 'yes' or 'no'")

class ContentRouter(BaseModel):
    router: str = Field(description="route to assistant or generate")
        
def get_latest_question(state: AgenticRagMessagesState) -> Optional[str]:
    try:
        if not state.get("messages") or not isinstance(state["messages"], (list, tuple)) or len(state["messages"]) == 0:
            chat_logger.warning(f"{time.time()},No valid messages found in state for getting latest question")
            return None
        question_list = []
        for message in state["messages"]:
            if message.__class__.__name__ == "HumanMessage" and hasattr(message, "content"):
                if type(message.content) == list:
                    for content in message.content:
                        if type(content) == dict:
                            if content["type"] == "text":
                                question_list.append(content["text"])
                else:
                    question_list.append(message.content)

        return "\n".join(question_list[:-1])
    
    except Exception as e:
        chat_logger.error(f"{time.time()},Error getting latest question: {traceback.format_exc()}")
        return None

def escape_markdown_special_chars(text: str) -> str:
    """
    将字符串中的Markdown特殊字符进行转义。
    
    Args:
        text (str): 待转义的原始字符串。
    
    Returns:
        str: 转义后的字符串，兼容Markdown格式。
    """
    # 定义需要转义的Markdown特殊字符
    # 按照 CommonMark 规范，以下字符需要转义
    # \ ` * _ { } [ ] ( ) # + - . !
    # 还有一些可能需要转义的字符，如 | < > 等，取决于具体的Markdown渲染器
    
    special_chars_pattern = r'([\\`*_{}\[\]()#+\-.!|<>])'
    
    # 使用re.sub，在每个特殊字符前添加一个反斜杠
    # r'\1' 表示匹配到的第一个分组，即括号里的特殊字符本身
    escaped_text = re.sub(special_chars_pattern, r'\\\1', text)
    
    return escaped_text

def get_router_question(state: AgenticRagMessagesState) -> Optional[str]:
    try:
        if not state.get("messages") or not isinstance(state["messages"], (list, tuple)) or len(state["messages"]) == 0:
            chat_logger.warning(f"{time.time()},No valid messages found in state for getting latest question")
            return None
        question_list = []
        for message in state["messages"]:
            if message.__class__.__name__ == "HumanMessage" and hasattr(message, "content"):
                if type(message.content) == list:
                    for content in message.content:
                        if type(content) == dict:
                            if content["type"] == "text":
                                question_list.append(content["text"])
                else:
                    question_list.append(message.content)

        return question_list[-1]
    
    except Exception as e:
        chat_logger.error(f"{time.time()},Error getting latest question: {traceback.format_exc()}")
        return None
    
def filter_messages(messages: list) -> list:
    filtered = [] 
    for msg in messages:
        if msg.__class__.__name__ == 'AIMessage':
            if type(msg.content) == str and msg.content.strip() != "":
                if (msg.content.startswith("1. ") and msg.content.find("2. ")  > 0 and msg.content.find("3. ") > 0 and msg.content.find("4. ") < 0) or \
                   (msg.content.startswith("I.") and msg.content.find("II.")  > 0 and msg.content.find("III.") > 0 and msg.content.find("IV.") < 0) :
                    chat_logger.info(f"{time.time()},AIMessage content matches numbered list, skipping: {msg.content}") 
                    continue
                filtered.append(AIMessage(content=msg.content))

        if msg.__class__.__name__ in ['SystemMessage',  'HumanMessage']:
            if type(msg.content) == str and msg.content.strip() != "":
                    m = re.match(r'.*<question>(.*)</question>.*', msg.content, re.M)
                    if m:
                        filtered.append(HumanMessage(content=m.group(1)))
                    else:
                        filtered.append(msg)
        
            if type(msg.content) == list:
                for content in msg.content:
                    if type(content) == dict and content["type"] == "text" and content["text"].strip() != "":
                        filtered.append(HumanMessage(content=content["text"]))
                                        
    msgids = set([compute_md5(msg.content) for msg in filtered])
    msg_unique = []

    for msg in filtered:
        msgmd5 = compute_md5(msg.content)
        if msgmd5 in msgids:
            msg_unique.append(msg)
            msgids.remove(msgmd5)

    msg_unique = msg_unique[-15:] if len(msg_unique) > 15 else msg_unique

    return [msg.content for msg in msg_unique]

async def store_memory(question: str, config: RunnableConfig, store: BaseStore) -> str:

    try:
        if type(question) == str:
            namespace = ("memories", config["configurable"]["thread_id"])
    
            if question.startswith("记住 ") or question.lower().startswith("remember ") \
                or question.startswith("记住:") or question.startswith("记住：") \
                or question.lower().startswith("remember:"):
                memory = escape(question)
                await store.aput(namespace, str(uuid.uuid4()), {"data": memory})
                chat_logger.info(f"{time.time()},Stored memory: {memory}")
 
            memories = await store.asearch(namespace, query=str(question), limit=3)
            user_info = "\n".join([d.value["data"] for d in memories])
            chat_logger.info(f"{time.time()},Found memory: {user_info}")
            return user_info
    except Exception as e:
        chat_logger.error(f"{time.time()},Error in store_memory: {traceback.format_exc()}")
        return ""

def create_chain(llm_chat, template_file: str, structured_output=None):

    if not hasattr(create_chain, "prompt_cache"):
        create_chain.prompt_cache = {}
        create_chain.lock = threading.Lock()

    try:
        if template_file in create_chain.prompt_cache:
            prompt_template = create_chain.prompt_cache[template_file]
            chat_logger.info(f"Using cached prompt template for {template_file}")
        else:
            with create_chain.lock:
                if template_file not in create_chain.prompt_cache:
                    chat_logger.info(f"{time.time()},Loading and caching prompt template from {template_file}")
                    create_chain.prompt_cache[template_file] = PromptTemplate.from_file(template_file, encoding="utf-8")
                prompt_template = create_chain.prompt_cache[template_file]
        prompt = ChatPromptTemplate.from_messages([("human", prompt_template.template)])
        return prompt | (llm_chat.with_structured_output(structured_output) if structured_output else llm_chat)
    except FileNotFoundError:
        chat_logger.error(f"{time.time()},Template file {template_file} not found, current work dir:" + os.getcwd())
        raise

async def rag_agent(state: AgenticRagMessagesState, config: RunnableConfig, llm_chat, tool_config: ToolConfig, store:BaseStore) -> dict:
        
    try:
        question = get_router_question(state)
        user_info = await store_memory(question, config, store)
        messages = filter_messages(state["messages"])
        
        llm_chat_with_tool = llm_chat.bind_tools(tool_config.get_tools())

        agent_chain = create_chain(llm_chat_with_tool, settings.prompt_template_agent)
        data_date = time.strftime("%Y年%m月%d日", time.localtime())
        chat_logger.info(f"{time.time()},Agent processing user query, Question: {question}, Tools: {tool_config.tool_descs}, user_info: {user_info}, messages:{messages}, data_date:{data_date}")
        response = await agent_chain.ainvoke({"question": question, "tools": tool_config.tool_descs, "user_info": user_info, "messages":messages, "data_date":data_date})
        chat_logger.info(f"{time.time()},Agent response,{response}")
        return {"messages": [response]}
    except Exception as e:
        chat_logger.error(f"{time.time()},Error in agent processing: {traceback.format_exc()}")
        return {"messages": [{"role": "system", "content": "处理请求时出错"}]}

async def grade_documents(state: AgenticRagMessagesState) -> dict:

    if not state.get("messages"):
        chat_logger.error(f"{time.time()},Messages state is empty")
        return {
            "messages": [{"role": "system", "content": "状态为空，无法评分"}],
            "relevance_score": None
        }
        
    try:
        question = get_router_question(state)
        context = state["messages"][-1].content

        conf = Config(maxTokens=16384, model="qwen-max-latest", sendMemory=True, temperature=1.0)
        llm_chat = init_llm_model(conf)
        grade_chain = create_chain(llm_chat, settings.prompt_template_grade,  structured_output=DocumentRelevanceScore)
        chat_logger.info(f"{time.time()},Evaluating relevance - Question: {question}, Context: {context}")
        scored_result = await grade_chain.ainvoke({"question": question, "context": context})
        chat_logger.info(f"{time.time()},Scoring result: {scored_result}")
        score = scored_result.binary_score
        chat_logger.info(f"{time.time()},Document relevance score: {score}")

        return {
            "messages": state["messages"],
            "relevance_score": score
        }
    except (IndexError, KeyError) as e:
        chat_logger.error(f"{time.time()},Message access error: {traceback.format_exc()}")
        return {
            "messages": [{"role": "system", "content": "无法评分文档"}],
            "relevance_score": None
        }
    except Exception as e:
        chat_logger.error(f"{time.time()},Unexpected error in grading: {traceback.format_exc()}")
        return {
            "messages": [{"role": "system", "content": "评分过程中出错"}],
            "relevance_score": None
        }

async def rewrite(state: AgenticRagMessagesState) -> dict:

    chat_logger.info(f"{time.time()},Rewriting query,{state['messages'][-1]}")

    try:
        question = get_router_question(state)
        conf = Config(maxTokens=16384, model="qwen-max-latest", sendMemory=True, temperature=0.5)
        llm_chat = init_llm_model(conf)
        rewrite_chain = create_chain(llm_chat, settings.prompt_template_rewrite)
        chat_logger.info(f"{time.time()},rewrite question:{question}")
        response = await rewrite_chain.ainvoke({"question": question})
        chat_logger.info(f"{time.time()},rewrite result:{response.content}")
        rewrite_count = state.get("rewrite_count", 0) + 1
        return {"messages": [response], "rewrite_count": rewrite_count}
    except (IndexError, KeyError) as e:
        chat_logger.error(f"{time.time()},Message access error in rewrite: {traceback.format_exc()}")
        return {"messages": [{"role": "system", "content": "无法重写查询"}]}

def generate(state: AgenticRagMessagesState, llm_chat) -> dict:
    
    try:
        messages = filter_messages(state["messages"])
        question = get_router_question(state)
        message = state["messages"][-1]

        chat_logger.info(f"{time.time()},Process generate message {message}.")

        context = ""
        if type(message) == ToolMessage:
            #搜索结果，添加到生成图像的前面
            if message.name == "tavily_search":
                datas = json.loads(message.content)
                for idx, rs in enumerate(datas["results"]):
                    context += f"{idx+1},{rs['content']}\n"
            # 调用工具生成图片
            elif message.name == "create_image":
                context += f'图片已经生成并返回给用户端展示，你只需回复图片已按需求生成.\n'
            #其他工具
            else:
                if type(message.content) == str:
                    context += message.content + "\n"
                elif type(message.content) == list:
                    for content in message.content:
                        if type(content) == dict and content["type"] == "text":
                            context += content["text"] + "\n"
        elif type(message) == HumanMessage:
            context = ""
        else:
            if type(message.content) == str:
                context += message.content + "\n"
            elif type(message.content) == list:
                for content in message.content:
                    if type(content) == dict and content["type"] == "text":
                        context += content["text"] + "\n"

        chat_logger.info(f"{time.time()},Generating final response, Question: {question}, Context: {context}, messages: {messages}")
        generate_chain = create_chain(llm_chat, settings.prompt_template_generate,)
        response =  generate_chain.invoke({"context": context, "question": question, "messages": messages})
        chat_logger.info(f"{time.time()},Generated response: {response}")
        return {"messages": [response]}
    except (IndexError, KeyError) as e:
        chat_logger.error(f"{time.time()},Message access error in generate: {traceback.format_exc()}")
        return {"messages": [{"role": "system", "content": "无法生成回复"}]}

async def recommend_question(state: AgenticRagMessagesState) -> dict:

    try:
        question = get_router_question(state)
        context = state["messages"][-1].content
        conf = Config(maxTokens=16384, model="qwen-max-latest", sendMemory=True, temperature=1.0)

        llm_chat = init_llm_model(conf)
        recommend_chain = create_chain(llm_chat, settings.prompt_template_recommend)
        chat_logger.info(f"{time.time()},Recommend Similar Question - Question: {question}, Context: {context}")
        response = await recommend_chain.ainvoke({"question": question, "context": context})
        chat_logger.info(f"{time.time()},Recommend result: {response}")
        return {
            "messages": [response]
        }
    except (IndexError, KeyError) as e:
        chat_logger.error(f"{time.time()},Message access error: {traceback.format_exc()}")
        return {
            "messages": [{"role": "system", "content": "遇到异常，无法推荐相似问题。"}],
            "relevance_score": None
        }
    except Exception as e:
        chat_logger.error(f"{time.time()},Unexpected error in grading: {traceback.format_exc()}")
        return {
            "messages": [{"role": "system", "content": "遇到异常，无法推荐相似问题。"}],
        }

async def router_content(state: AgenticRagMessagesState) -> dict:

    try:
        question = get_router_question(state)
        conf = Config(maxTokens=16384, model="qwen-max-latest", sendMemory=True, temperature=1.0)
        llm_chat = init_llm_model(conf)
        router_chain = create_chain(llm_chat, settings.prompt_template_router,  structured_output=ContentRouter)
        chat_logger.info(f"{time.time()},Router Content - Question: {question}")
        router_result = await router_chain.ainvoke({"question": question})
        chat_logger.info(f"{time.time()},Router Content result: {router_result}")
        router = router_result.router
        return {
            "messages": state["messages"],
            "content_router": router
        }
    except (IndexError, KeyError) as e:
        chat_logger.error(f"{time.time()},Message access error: {traceback.format_exc()}")
        return {
            "messages": [{"role": "system", "content": "评估问题时遇到错误。"}],
            "content_router": "generate"
        }
    except Exception as e:
        chat_logger.error(f"{time.time()},Unexpected error in grading: {traceback.format_exc()}")
        return {
            "messages": [{"role": "system", "content": "评估问题时遇到错误。"}],
            "content_router": "generate"
        }
                
def route_after_tools(state: AgenticRagMessagesState, tool_config: ToolConfig) -> Literal["generate", "grade_documents"]:
    if not state.get("messages") or not isinstance(state["messages"], list):
        chat_logger.error(f"{time.time()},Messages state is empty or invalid, defaulting to generate")
        return "generate"
    try:
        last_message = state["messages"][-1]
        if not hasattr(last_message, "name") or last_message.name is None:
            chat_logger.info(f"{time.time()},Last message has no name attribute, routing to generate")
            return "generate"
        tool_name = last_message.name
        if tool_name not in tool_config.get_tool_names():
            chat_logger.info(f"{time.time()},Unknown tool {tool_name}, routing to generate")
            return "generate"

        target = tool_config.get_tool_routing_config().get(tool_name, "generate")
        chat_logger.info(f"{time.time()},Tool {tool_name} routed to {target} based on config")
        return target

    except IndexError:
        chat_logger.error(f"{time.time()},No messages available in state, defaulting to generate")
        return "generate"
    except AttributeError:
        chat_logger.error(f"{time.time()},Invalid message object, defaulting to generate")
        return "generate"
    except Exception as e:
        chat_logger.error(f"{time.time()},Unexpected error in route_after_tools: {traceback.format_exc()}, defaulting to generate")
        return "generate"

def route_after_grade(state: AgenticRagMessagesState) -> Literal["generate", "rewrite"]:
   
    if not isinstance(state, dict):
        chat_logger.error(f"{time.time()},State is not a valid dictionary, defaulting to rewrite")
        return "rewrite"
    if "messages" not in state or not isinstance(state["messages"], (list, tuple)):
        chat_logger.error(f"{time.time()},State missing valid messages field, defaulting to rewrite")
        return "rewrite"
    
    if not state["messages"]:
        chat_logger.warning(f"{time.time()},Messages list is empty, defaulting to rewrite")
        return "rewrite"

    relevance_score = state.get("relevance_score")
    rewrite_count = state.get("rewrite_count", 0)
    chat_logger.info(f"{time.time()},Routing based on relevance_score: {relevance_score}, rewrite_count: {rewrite_count}")

    if rewrite_count >= 2:
        chat_logger.info(f"{time.time()},Max rewrite limit reached, proceeding to generate")
        return "generate"

    try:
        if not isinstance(relevance_score, str):
            chat_logger.warning(f"{time.time()},Invalid relevance_score type: {type(relevance_score)}, defaulting to rewrite")
            return "rewrite"
        
        if relevance_score.lower() == "yes":
            chat_logger.info(f"{time.time()},Documents are relevant, proceeding to generate")
            return "generate"

        chat_logger.info(f"{time.time()},Documents are not relevant or scoring failed, proceeding to rewrite")
        return "rewrite"

    except AttributeError:
        chat_logger.error(f"{time.time()},relevance_score is not a string or is None, defaulting to rewrite")
        return "rewrite"
    except Exception as e:
        chat_logger.error(f"{time.time()},Unexpected error in route_after_grade: {traceback.format_exc()}, defaulting to rewrite")
        return "rewrite"

def route_after_content(state: AgenticRagMessagesState) -> Literal["generate", "assistant"]:
   
    if not isinstance(state, dict):
        chat_logger.error(f"{time.time()},State is not a valid dictionary, defaulting to rewrite")
        return "generate"
    if "messages" not in state or not isinstance(state["messages"], (list, tuple)):
        chat_logger.error(f"{time.time()},State missing valid messages field, defaulting to rewrite")
        return "generate"
    
    if not state["messages"]:
        chat_logger.warning(f"{time.time()},Messages list is empty, defaulting to rewrite")
        return "generate"

    content_router = state.get("content_router", "generate")
    return content_router.lower()

async def create_rag_graph(config, llm_chat, tool_config: ToolConfig, store:BaseStore) -> StateGraph:

    async def my_agent(state:AgenticRagMessagesState, config:RunnableConfig, store:BaseStore):
        return await rag_agent(state, config, llm_chat, tool_config, store)
    
    workflow = StateGraph(AgenticRagMessagesState)
    workflow.add_node("agent", my_agent)
    workflow.add_node("call_tools", ToolNode(tool_config.get_tools()))
    workflow.add_node("rewrite", rewrite)
    workflow.add_node("generate", lambda state: generate(state, llm_chat=llm_chat))
    workflow.add_node("grade_documents", grade_documents)
    workflow.add_node("recommend_question", recommend_question)

    workflow.add_edge(START, end_key="agent")
    workflow.add_conditional_edges(source="agent", path=tools_condition, path_map={"tools": "call_tools", END: "generate"})
    workflow.add_conditional_edges(source="call_tools", path=lambda state: route_after_tools(state, tool_config),path_map={"generate": "generate", "grade_documents": "grade_documents"})
    workflow.add_conditional_edges(source="grade_documents", path=route_after_grade, path_map={"generate": "generate", "rewrite": "rewrite"})
    workflow.add_edge(start_key="generate", end_key="recommend_question")
    workflow.add_edge(start_key="rewrite", end_key="agent")
    workflow.add_edge(start_key="recommend_question", end_key=END)
    return workflow

async def get_retriever(tenant_id, datasource, embeddings):
    
    retrievers = []
    if datasource:
        langchain_service = LangChainService(tenant_id, "datasource")
        dataSourceVectorStore = langchain_service.create_vector_store(datasource)
        retrievers.append(dataSourceVectorStore.as_retriever(search_kwargs={'k': 3}))

    if embeddings:
        langchain_service = LangChainService(tenant_id, "embeddings")
        for embedding_id in embeddings:
            collection_name = embedding_id
            vector_store =  langchain_service.create_vector_store(collection_name)
            retrievers.append(vector_store.as_retriever(search_kwargs={'k': 3}))
    if len(retrievers) > 0:
        retriever = MergerRetriever(retrievers=retrievers)
    else:
        retriever = None
        
    return retriever

async def get_retriever_tools(tenant_id, datasource, embeddings):
    tools = []
    retriever = await get_retriever(tenant_id, datasource, embeddings)
    if retriever:
        retriever_tool = create_retriever_tool(
            retriever,
            name="retrieve",
            description="这是一个多文档查询工具。搜索并返回有关问题的信息。"
        )
        
        tools.append(retriever_tool)
    return tools

async def get_chat_graph_with_rag(model:BaseChatModel, config, tenant_id, datasource, embeddings, store:BaseStore) -> StateGraph:
    
    tools = await get_retriever_tools(tenant_id, datasource, embeddings)
    tool_config = ToolConfig(tools)
    workflow = await create_rag_graph(config, model, tool_config, store)
    return workflow

async def assitant_agent(state: AgenticRagMessagesState, config: RunnableConfig, llm_chat, tool_config: ToolConfig, store:BaseStore) -> dict:
    
    try:
        question = get_router_question(state)
        user_info = await store_memory(question, config, store)
        messages = filter_messages(state["messages"])

        llm_chat_with_tool = llm_chat.bind_tools(tool_config.get_tools())

        agent_chain = create_chain(llm_chat_with_tool, settings.prompt_template_assistant)
        data_date = time.strftime("%Y年%m月%d日", time.localtime())
        chat_logger.info(f"{time.time()},Assistant processing user query, Question: {question}, Tools: {tool_config.tool_descs}, user_info: {user_info}, messages: {messages}, data_date:{data_date}")
        response = await agent_chain.ainvoke({"question": question, "tools": tool_config.tool_descs, "user_info":user_info, "messages":messages, "data_date":data_date})
        chat_logger.info(f"{time.time()},Assistant response,{response}")
        return {"messages": [response]}
    
    except Exception as e:
        chat_logger.error(f"{time.time()},Error in Assistant processing: {traceback.format_exc()}")
        return {"messages": [{"role": "system", "content": "处理请求时出错"}]}
                    
async def get_search_tools():
    tools = []
    tavily_search_tool = TavilySearch(tavily_api_key=settings.tavily_api_key, max_results=5,topic="general",include_images=False,include_image_descriptions=False)
    tools.append(tavily_search_tool)
    return tools 

async def get_chat_graph_with_search(model:BaseChatModel, config:RunnableConfig, store:BaseStore) -> StateGraph:
    
    tools = await get_search_tools()
    tool_config = ToolConfig(tools)
    async def my_agent(state:AgenticRagMessagesState, config:RunnableConfig, store:BaseStore):
        return await assitant_agent(state, config, model, tool_config, store)
    
    workflow = StateGraph(AgenticRagMessagesState)
    workflow.add_node("router_content", router_content)
    workflow.add_node("assistant", my_agent)
    workflow.add_node("call_tools", ToolNode(tool_config.get_tools()))
    workflow.add_node("generate", lambda state: generate(state, llm_chat=model))
    workflow.add_node("recommend_question", recommend_question)

    workflow.add_edge(START, "router_content")
    workflow.add_conditional_edges(source="router_content", path=route_after_content, path_map={"assistant": "assistant", "generate": "generate"})
    workflow.add_conditional_edges(source="assistant", path=tools_condition, path_map={"tools": "call_tools", END: "generate"})
    workflow.add_edge("call_tools", "generate")
    workflow.add_edge("generate", "recommend_question")
    workflow.add_edge("recommend_question", END)
    
    return workflow

async def image_agent(state:AgenticRagMessagesState, config: RunnableConfig, store:BaseStore):
    
    model = ChatOpenAI(
        model="qwen-vl-plus",
        temperature=0.7,
        max_tokens=16384,
        api_key=settings.qwen_api_key,
        openai_api_base=settings.qwen_base_url
    )
    image_message = state["messages"][-1]
    messages = filter_messages(state["messages"])
    prompt = ChatPromptTemplate.from_messages([AIMessage(content=msg) for msg in messages] + [image_message])
    chain = prompt | model 
    response = await chain.ainvoke({})
    return {"messages": [response]}

async def get_chat_graph_with_image(model:BaseChatModel, config:RunnableConfig, store:BaseStore) -> StateGraph:

    async def my_agent(state:AgenticRagMessagesState, config:RunnableConfig, store:BaseStore):
        return await image_agent(state, config, store)
    
    workflow = StateGraph(AgenticRagMessagesState)
    workflow.add_node("generate", my_agent) 
    workflow.add_node("recommend_question", recommend_question)

    workflow.add_edge(START, "generate")
    workflow.add_edge("generate", "recommend_question")
    workflow.add_edge("recommend_question", END)
   
    return workflow

async def get_chat_graph_with_draw(model:BaseChatModel, config:RunnableConfig, store:BaseStore) -> StateGraph:

    async def draw_image(state:AgenticRagMessagesState, config:RunnableConfig, store:BaseStore):

        image_message = state["messages"][-1]
        image_mcp = await create_image(image_message.content)
        image_content = image_mcp.to_image_content()
        tool_output = [image_content]
        return {"messages": [ToolMessage(name="create_image", content="create_image", tool_call_id=str(uuid.uuid4()), artifact=tool_output)]}

    workflow = StateGraph(AgenticRagMessagesState)
    workflow.add_node("generate", draw_image) 

    workflow.add_edge(START, "generate")
    workflow.add_edge("generate", END)
   
    return workflow

async def get_chat_graph(model:BaseChatModel, config, mcp_client: MultiServerMCPClient, store:BaseStore) -> StateGraph:
    
    try:
        tools = await mcp_client.get_tools()
    except BrokenResourceError as err:
        chat_logger.info(f"{time.time()}, Get MCP Tools Failed, {traceback.format_exc()}")
        tools = []
        
    tool_config = ToolConfig(tools)
    async def my_agent(state:AgenticRagMessagesState, config:RunnableConfig, store:BaseStore):
        return await assitant_agent(state, config, model, tool_config, store)
    
    workflow = StateGraph(AgenticRagMessagesState)
    workflow.add_node("router_content", router_content)
    workflow.add_node("assistant", my_agent)
    workflow.add_node("call_tools", ToolNode(tool_config.get_tools()))
    workflow.add_node("generate", lambda state: generate(state, llm_chat=model))
    workflow.add_node("recommend_question", recommend_question)
    
    workflow.add_edge(START, "router_content")
    workflow.add_conditional_edges(source="router_content", path=route_after_content, path_map={"assistant": "assistant", "generate": "generate"})
    workflow.add_conditional_edges(source="assistant", path=tools_condition, path_map={"tools": "call_tools", END: "generate"})
    workflow.add_edge("call_tools", "generate")
    workflow.add_edge("generate", "recommend_question")
    workflow.add_edge("recommend_question", END)
    
    return workflow
    
def retrieve_prompt_from_embeddings(tenant_id:str, embeddings:list[str], userId:str):

    contents = []
    if embeddings:
        langchain_service = LangChainService(tenant_id, "embeddings")
        
        for embedding_id in embeddings:
            collection_name = embedding_id
            collection = langchain_service.get_or_create_collection(collection_name)
            results = collection.get(
                where={"userId": {"$eq": userId}}
            )

            contents += [
              doc for doc in results["documents"]
            ]
            
    prompt = "\n".join(contents) 
    return prompt
