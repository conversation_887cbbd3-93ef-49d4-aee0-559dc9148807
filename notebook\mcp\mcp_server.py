#!/usr/bin/env python
# -*- coding:utf-8 -*-

import aiohttp
import asyncio
import os
from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp import Image as McpImage
from PIL import PngImagePlugin
import io
import piexif
import traceback
import logging
from ..config import get_settings
settings = get_settings()

logger = logging.getLogger(__name__)

# --- Keep the existing get_image_bytes function if needed elsewhere ---
# --- Or remove it if it's only used by the old create_image ---
def get_image_bytes(image, ext:str):
    with io.BytesIO() as output_bytes:
        if ext.lower() == 'png':
            metadata = None
            for key, value in image.info.items():
                if isinstance(key, str) and isinstance(value, str):
                    if metadata is None:
                        metadata = PngImagePlugin.PngInfo()
                    metadata.add_text(key, value)
            image.save(output_bytes, format="PNG", pnginfo=metadata)
        elif ext.lower() in ("jpg", "jpeg"):
            parameters = image.info.get('parameters', None)
            exif_bytes = piexif.dump({
                "Exif": { piexif.ExifIFD.UserComment: piexif.helper.UserComment.dump(parameters or "", encoding="unicode") }
            })
            image.save(output_bytes, format="JPEG", exif = exif_bytes, quality=80)
        elif ext.lower() == "webp":
            parameters = image.info.get('parameters', None)
            exif_bytes = piexif.dump({
                "Exif": { piexif.ExifIFD.UserComment: piexif.helper.UserComment.dump(parameters or "", encoding="unicode") }
            })
            image.save(output_bytes, format="WEBP", exif = exif_bytes, method=6, quality=60, alpha_quality=50, save_all=True)

        bytes_data = output_bytes.getvalue()
        return bytes_data
    
mcp = FastMCP("mcp_server", host=settings.server_host, port=settings.server_port, stateless_http=True, json_response=True)

# --- Keep existing tools ---
@mcp.tool()
def multiply(a: float, b: float) -> float:
    """这是计算两个数的积的工具，返回最终的计算结果"""
    return a * b
@mcp.tool()
def add(a: float, b: float) -> float:
    """这是计算两个数的和的工具，返回最终的计算结果"""
    return a + b
@mcp.tool()
def minus(a: float, b: float) -> float:
    """这是计算两个数的差的工具，返回最终的计算结果"""
    return a - b
@mcp.tool()
def divide(a: float, b: float) -> float:
    """这是计算两个数的商的工具，返回最终的计算结果"""
    return a / b

@mcp.tool()
async def get_weather(location: str) -> str:
    """这是获取城市天气的工具， 返回获取到的天气结果"""
    # Keep the dummy implementation or replace with a real weather API call
    await asyncio.sleep(0.1) # Simulate async work if needed
    return f"It's always sunny in {location}" # Made it dynamic for fun

# --- Configuration for Alibaba Cloud Flux API ---
QWEN_API_KEY = os.environ.get("QWEN_API_KEY")
if not QWEN_API_KEY:
    logger.error("Error: QWEN_API_KEY environment variable not set.")
    QWEN_API_KEY = settings.qwen_api_key
    
QWEN_BASE_URL = "https://dashscope.aliyuncs.com/api/v1"
TEXT2IMAGE_SUBMIT_URL = f"{QWEN_BASE_URL}/services/aigc/text2image/image-synthesis"
TASK_STATUS_URL_TEMPLATE = QWEN_BASE_URL + "/tasks/{}" # Placeholder for task_id
# MODEL_NAME = "flux-dev" # Or "flux-dev"
MODEL_NAME = "wanx2.1-t2i-turbo"
POLL_INTERVAL_SECONDS = 3  # How often to check task status
MAX_POLL_ATTEMPTS = 40     # Max attempts (e.g., 40 * 3s = 120s = 2 minutes timeout)
# --- End Configuration ---

# --- Modified create_image function ---
@mcp.tool()
async def create_image(prompt: str, size: str = "1024*1024") -> McpImage | str:
    """
    这是创建图片生成工具，调用阿里云Flux API根据prompt生成图片，返回生成的图片。
    当用户提到画一幅画、绘制图片或创建图片时可以被调用。
    :param prompt: 图片描述文本 (中文 <= 500 chars, 英文 <= 500 words).
    :param size: 图片分辨率, e.g. 512*1024, 768*512, 768*1024, 1024*576, 576*1024, 1024*1024. Default: "1024*1024".
    :return: McpImage object on success, or an error message string on failure.
    """
    if not QWEN_API_KEY:
        return "Error: QWEN_API_KEY is not configured on the server."

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {QWEN_API_KEY}",
        "X-DashScope-Async": "enable", 
    }
    payload = {
        "model": MODEL_NAME,
        "input": {
            "prompt": prompt
        },
        "parameters": {
            "size": size
        }
    }

    task_id = None
    try:
        async with aiohttp.ClientSession() as session:
            # 1. Submit the image generation task
            logger.info(f"Submitting image task for prompt: '{prompt}'")
            task_id = None
            async with session.post(TEXT2IMAGE_SUBMIT_URL, headers=headers, json=payload) as response:
                if response.status == 200:
                    submit_result = await response.json()
                    task_id = submit_result.get("output", {}).get("task_id")
                    task_status = submit_result.get("output", {}).get("task_status")
                    logger.info(f"Task submitted. ID: {task_id}, Initial Status: {task_status}")
                else:
                    error_text = await response.text()
                    logger.info(f"Error submitting task: HTTP {response.status}, Response: {error_text}")
                    return f"Error submitting task: HTTP {response.status} - {error_text}"

            if not task_id:
                error_msg = submit_result.get("message", "Failed to get task_id from submission response.")
                logger.info(f"Error submitting task: {error_msg}")
                return f"Error submitting task: {error_msg}"
            
            # 2. Poll for task status and result
            task_url = TASK_STATUS_URL_TEMPLATE.format(task_id)
            logger.info(f"Polling task status for ID: {task_id}")
            for attempt in range(MAX_POLL_ATTEMPTS):
                await asyncio.sleep(POLL_INTERVAL_SECONDS) # Wait before checking
                logger.info(f"Polling attempt {attempt + 1}/{MAX_POLL_ATTEMPTS}...")
                async with session.get(task_url, headers={"Authorization": f"Bearer {QWEN_API_KEY}"}) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.info(f"Error polling task status: HTTP {response.status}, Response: {error_text}")
                        # Continue polling unless it's a fatal error like 401/404?
                        # For simplicity, we'll keep polling for now.
                        continue # Or return f"Error polling task: HTTP {response.status}"

                    status_result = await response.json()
                    task_status = status_result.get("output", {}).get("task_status")
                    logger.info(f"Current task status: {task_status}")

                    if task_status == "SUCCEEDED":
                        results = status_result.get("output", {}).get("results")
                        if results and isinstance(results, list) and len(results) > 0:
                            image_url = results[0].get("url")
                            if image_url:
                                logger.info(f"Task succeeded. Image URL: {image_url}")
                                # 3. Download the image
                                async with session.get(image_url) as img_response:
                                    if img_response.status == 200:
                                        image_bytes = await img_response.read()
                                        logger.info(f"Image downloaded successfully ({len(image_bytes)} bytes).")
                                        # Determine format (simple check based on URL extension)
                                        image_format = image_url.split('.')[-1].lower()
                                        if '?' in image_format: # Handle potential query params
                                            image_format = image_format.split('?')[0]
                                        # Basic validation for common formats
                                        if image_format not in ['png', 'jpg', 'jpeg', 'webp']:
                                             logger.info(f"Warning: Unknown image format '{image_format}' from URL. Assuming png.")
                                             image_format = 'png' # Default assumption

                                        # 4. Return as McpImage
                                        mcp_image = McpImage(data=image_bytes, format=image_format)
                                        return mcp_image
                                    else:
                                        error_text = await img_response.text()
                                        logger.info(f"Failed to download image: HTTP {img_response.status}, Response: {error_text}")
                                        return f"Failed to download image: HTTP {img_response.status}"
                        else:
                            logger.info("Task succeeded but no image URL found in results.")
                            return "Task succeeded but no image URL found."
                        break # Exit polling loop on success

                    elif task_status == "FAILED":
                        error_code = status_result.get("output", {}).get("code", "UnknownCode")
                        error_message = status_result.get("output", {}).get("message", "Unknown error.")
                        logger.info(f"Task failed. Code: {error_code}, Message: {error_message}")
                        return f"Image generation failed: {error_message} (Code: {error_code})"

                    elif task_status in ["PENDING", "RUNNING"]:
                        # Continue polling
                        pass
                    else: # UNKNOWN or other unexpected status
                         logger.info(f"Unknown or unexpected task status: {task_status}")
                         return f"Unexpected task status: {task_status}"

            else: # Loop finished without success or explicit failure (timeout)
                logger.info("Polling timed out.")
                return f"Image generation timed out after {MAX_POLL_ATTEMPTS * POLL_INTERVAL_SECONDS} seconds."

    except aiohttp.ClientError as e:
        logger.info(f"Network error during image generation: {e}")
        traceback.print_exc()
        return f"Network error: {traceback.format_exc()}"
    except asyncio.TimeoutError:
        logger.info("Asyncio operation timed out.")
        traceback.print_exc()
        return f"Operation timed out.{traceback.format_exc()}"
    except Exception as e:
        logger.info(f"An unexpected error occurred: {traceback.format_exc()}")
        traceback.print_exc()
        return f"An unexpected error occurred: {traceback.format_exc()}"

if __name__ == "__main__":
    
    # Start the MCP server
    mcp.run(transport="sse") # Or "websocket" or default http
