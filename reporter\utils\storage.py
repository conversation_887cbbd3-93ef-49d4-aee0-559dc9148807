import json 
import os

def store_data(data, file_name):
    file_path = f"{file_name}.json"
    path = os.path.dirname(file_path)
    os.makedirs(path, exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)

def load_data(file_name):
    file_path = f"{file_name}.json"
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)

if __name__ == "__main__":
    data = {"a": 1, "b": 2}
    store_data(data, "data/test")
    print(load_data("data/test"))
