# main.py
from datetime import datetime, timedelta
from config import settings
from data_fetchers.mysql_data_fetcher import SQLDataFetcher
from data_fetchers.hive_data_fetcher import HiveDataFetcher
from report_generators.markdown_generator import MarkdownGenerator
from report_generators.ai_generator import AIReportGenerator
from report_generators.chart_generator import ChartGenerator
from utils.wecom_robot import WeComRobot
import sys 
from utils.storage import store_data, load_data
import os
import shutil

def main():
    # 初始化组件
    sql_fetcher = SQLDataFetcher(settings.FUXI_DATABASE_URL)
    hive_fetcher = HiveDataFetcher(settings.HIVE_HOST, settings.HIVE_PORT, database="crius")
    
    if len(sys.argv) > 1:
        yestoday_str = sys.argv[1]
        yestoday = datetime.strptime(yestoday_str, "%Y%m%d")
    else:
        yestoday = datetime.now() - timedelta(days=1)

    seven_days_ago = yestoday - timedelta(days=6)
    yestoday_str = yestoday.strftime("%Y-%m-%d")
    seven_days_str = seven_days_ago.strftime("%Y-%m-%d")

    ymd_list = []
    for i in range(7):
        current_date = yestoday - timedelta(days=i)
        ymd_list.append([
            str(current_date.year),
            str(current_date.month).zfill(2),
            str(current_date.day).zfill(2)
        ])

    # 创建数据目录
    data_path = f"data/{yestoday.strftime('%Y%m%d')}"
    os.makedirs(data_path, exist_ok=True)

    ipct_day_data = hive_fetcher.get_ipct_day_data(ymd_list)
    store_data(ipct_day_data, f"{data_path}/ipct_day_data")
    ipct_gen_image= hive_fetcher.get_ipct_gen_image(ymd_list)
    store_data(ipct_gen_image, f"{data_path}/ipct_gen_image")
    heli_income_sum = sql_fetcher.get_heli_income_sum(seven_days_str, yestoday_str)
    store_data(heli_income_sum, f"{data_path}/heli_income_sum")
    heli_day_data = hive_fetcher.get_heli_day_data(ymd_list)
    store_data(heli_day_data, f"{data_path}/heli_day_data")
    heli_hebao_active = sql_fetcher.get_heli_hebao_active()
    store_data(heli_hebao_active, f"{data_path}/heli_hebao_active")
    heli_hebao_policy = sql_fetcher.get_heli_hebao_policy()
    store_data(heli_hebao_policy, f"{data_path}/heli_hebao_policy")
    conversion_day_data = hive_fetcher.get_conversion_day_data(ymd_list[0])
    store_data(conversion_day_data, f"{data_path}/conversion_day_data")

    # 生成图表
    chart_gen = ChartGenerator(data_path)
    chart_files = chart_gen.generate_all_charts()
    print(f"Generated {len(chart_files)} charts.")

    # 生成Markdown文件
    md_gen = MarkdownGenerator(data_path)
    markdown_content = md_gen.generate_markdown(yestoday)
    markdown_file_path = f"{data_path}/operator_report_{yestoday_str}.md"
    with open(markdown_file_path, "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    markdown_content = md_gen.generate_report(yestoday)
    markdown_file_path = f"{data_path}/operator_report_human_{yestoday_str}.md"
    with open(markdown_file_path, "w", encoding="utf-8") as f:
        f.write(markdown_content)

    print(f"Markdown report saved to {markdown_file_path}")

    # 生成AI分析报告
    markdown_file_path = f"{data_path}/operator_report_{yestoday_str}.md"
    ai_gen = AIReportGenerator(settings, markdown_file_path)
    ai_report = ai_gen.summary()
    ai_report_file_path = f"{data_path}/ai_report_{yestoday_str}.txt"
    with open(ai_report_file_path, "w", encoding="utf-8") as f:
        f.write(ai_report)
    print(f"AI分析报告已保存到 {ai_report_file_path}")

    # 发送Markdown报告到企业微信
    wecom_robot = WeComRobot(settings.WECOM_ROBOT_WEBHOOK_URL)

    markdown_file_path = f"{data_path}/operator_report_human_{yestoday_str}.md"
    wecom_robot.send_markdown_file(markdown_file_path)

    ai_report_file_path = f"{data_path}/ai_report_{yestoday_str}.txt"
    wecom_robot.send_markdown_file(ai_report_file_path)

    before_path = f"data/{seven_days_ago.strftime('%Y%m%d')}"
    if os.path.exists(before_path):
        shutil.rmtree(before_path)

if __name__ == "__main__":
    main()

