#!/usr/bin/env python 
# -*- coding:utf-8 -*-

# import os
# os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
# os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

import logging
import logging.config

from .utils import RespCode, APIException
from fastapi import FastAPI, Request 
from fastapi.exceptions import HTTPException, RequestValidationError, WebSocketRequestValidationError
from fastapi.staticfiles import StaticFiles
from starlette.templating import Jinja2Templates
from starlette.exceptions import HTTPException as StarletteHTTPException
import contextlib 
from langchain_mcp_adapters.client import MultiServerMCPClient

from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse, RedirectResponse

from .config import get_settings
from .routers import api_router, custom_docs
from .views import views_router
from .libs.postgres import lifespan_db
from .utils import custom_logger, custom_middleware

settings = get_settings()

#配置日志
if settings.profile == "DEV":
    # logging.config.fileConfig("./notebook/config/log.debug.ini", disable_existing_loggers=False, encoding='utf-8')
    logging.config.fileConfig("./notebook/config/log.info.ini", disable_existing_loggers=False, encoding='utf-8')
else:
    logging.config.fileConfig("./notebook/config/log.info.ini", disable_existing_loggers=False, encoding='utf-8')
    
root_logger = logging.getLogger("root")

from .mpc.mcp_server import mcp

@contextlib.asynccontextmanager
async def lifespan_mcp(app: FastAPI):

    async with lifespan_db(app):
        try:

            root_logger.info("Initializing mcp server and client ...")
            client = MultiServerMCPClient({
            "mcp_server": {
                "url": f"http://{settings.mcp_host}:{settings.server_port}/mcp_server/mcp/",
                "transport": "streamable_http"
                }
            })
            app.state.mcp_client = client 
            async with contextlib.AsyncExitStack() as stack:
                await stack.enter_async_context(mcp.session_manager.run())
                yield
        finally:
            root_logger.info("Shutting down mcp server and client ...")

# 实例化 fastapi 对象
app = FastAPI(docs_url=None, redoc_url=None,
              title=settings.project_title,
              description=settings.project_description,
              version=settings.project_version, 
              lifespan=lifespan_mcp)

#自定义中间件
custom_middleware(app)

# 自定义 docs 界面
custom_docs(app)

app.mount("/mcp_server", mcp.streamable_http_app(), name = "mcp_server")

@app.exception_handler(404)
async def internal_error_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=404,
        content=jsonable_encoder(RespCode.resp_err("页面走丢了~")),
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: Exception):
    root_logger.error(str(exc))
    return JSONResponse(
        status_code=500,
        content=jsonable_encoder(RespCode.resp_err("小助手开小差了，换个问题试试吧。")),
    )

@app.exception_handler(StarletteHTTPException)
async def starlette_exception_handler(request: Request, exc: StarletteHTTPException):
    root_logger.error(str(exc))
    return JSONResponse(
        status_code=500,
        content=jsonable_encoder(RespCode.resp_err("小助手开小差了，换个问题试试吧。")),
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    root_logger.error(str(exc))
    return JSONResponse(
        status_code=500,
        content=jsonable_encoder(RespCode.resp_err("小助手开小差了，换个问题试试吧。")),
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    root_logger.error(str(exc))
    return JSONResponse(
        status_code=400,
        content=jsonable_encoder(RespCode.resp_err("小助手开小差了，换个问题试试吧。")),
    )

@app.exception_handler(WebSocketRequestValidationError)
async def websocket_exception_handler(request: Request, exc: WebSocketRequestValidationError):
    root_logger.error(str(exc))
    return JSONResponse(
        status_code=500,
        content=jsonable_encoder(RespCode.resp_err("小助手开小差了，换个问题试试吧。")),
    )

@app.exception_handler(APIException)
async def ai_exception_handler(request: Request, exc: APIException):
    root_logger.error(str(exc))
    return JSONResponse(
        status_code=500,
        content=jsonable_encoder(RespCode.resp_err("小助手开小差了，换个问题试试吧。")),
    )

# 挂载 api 路由
app.include_router(api_router)
# 挂载 view 路由
app.include_router(views_router)

# 挂载静态文件目录
app.mount(settings.static_url_prefix, StaticFiles(directory=settings.static_dir), name="static")
# 用户上传的文件
app.mount(settings.media_url_prefix, StaticFiles(directory=settings.media_dir), name="media")

@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    return RedirectResponse(url="/static/favicon.ico")

# 挂载 jinja2 模板引擎
app.state.jinja = Jinja2Templates(directory=settings.jinja2_templates_dir)


